package services

import (
	"context"
	"fmt"

	timeparser "neoapi/internal/time"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

// RouteService 航线服务
type RouteService struct {
	driver neo4j.DriverWithContext
}

// NewRouteService 创建新的航线服务
func NewRouteService(driver neo4j.DriverWithContext) *RouteService {
	return &RouteService{
		driver: driver,
	}
}

// RouteDetailInfo 航线详细信息
type RouteDetailInfo struct {
	RouteID             string  `json:"route_id"`
	RouteName           string  `json:"route_name"`
	RouteCode           string  `json:"route_code,omitempty"`
	OriginPortName      string  `json:"origin_port_name"`
	DestinationPortName string  `json:"destination_port_name"`
	DistanceKm          float64 `json:"distance_km"`
	RouteType           string  `json:"route_type"`
	NavigationLevel     string  `json:"navigation_level"`
	IsActive            bool    `json:"is_active"`
}

// RouteStatistics 航线统计信息
type RouteStatistics struct {
	Period             string  `json:"period"`
	TotalShipCount     int     `json:"total_ship_count"`
	TotalVoyageCount   int     `json:"total_voyage_count"`
	TotalCargoTon      float64 `json:"total_cargo_ton"`
	TotalTurnoverTonKm float64 `json:"total_turnover_tonkm"`
	AvgLoadRatio       float64 `json:"avg_load_ratio"`
	AvgVoyageTimeHours float64 `json:"avg_voyage_time_hours"`
	UtilizationRate    float64 `json:"utilization_rate"`
}

// RouteCargoBreakdown 航线货物构成
type RouteCargoBreakdown struct {
	CargoType            string  `json:"cargo_type"`
	CargoTon             float64 `json:"cargo_ton"`
	Percentage           float64 `json:"percentage"`
	ShipCount            int     `json:"ship_count"`
	VoyageCount          int     `json:"voyage_count"`
	AvgCargoPerVoyageTon float64 `json:"avg_cargo_per_voyage_ton"`
}

// RouteShipAnalysis 航线船舶分析
type RouteShipAnalysis struct {
	ShipName        string  `json:"ship_name"`
	MMSI            string  `json:"mmsi"`
	VoyageCount     int     `json:"voyage_count"`
	TotalCargoTon   float64 `json:"total_cargo_ton"`
	AvgLoadRatio    float64 `json:"avg_load_ratio"`
	AvgCargoPerTrip float64 `json:"avg_cargo_per_trip"`
}

// RouteProfileData 航线画像数据
type RouteProfileData struct {
	BasicInfo       RouteDetailInfo       `json:"basic_info"`
	RecentStats     RouteStatistics       `json:"recent_stats"`
	HistoricalStats []RouteStatistics     `json:"historical_stats,omitempty"`
	CargoBreakdown  []RouteCargoBreakdown `json:"cargo_breakdown,omitempty"`
	TopShips        []RouteShipAnalysis   `json:"top_ships,omitempty"`
	Summary         RouteProfileSummary   `json:"summary"`
}

// RouteProfileSummary 航线画像摘要
type RouteProfileSummary struct {
	TotalMonthsData     int      `json:"total_months_data"`
	AvgMonthlyCargoTon  float64  `json:"avg_monthly_cargo_ton"`
	AvgMonthlyShipCount int      `json:"avg_monthly_ship_count"`
	PrimaryCargoTypes   []string `json:"primary_cargo_types"`
	PerformanceRating   string   `json:"performance_rating"`
	EfficiencyLevel     string   `json:"efficiency_level"`
}

// GetRouteProfile 获取航线画像
func (s *RouteService) GetRouteProfile(ctx context.Context, routeIdentifier string, timeQuery *timeparser.TimeQuery, includeHistory bool, includeCargoBreakdown bool, includeShipAnalysis bool) (*RouteProfileData, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	// 1. 获取航线基本信息
	basicInfo, err := s.getRouteBasicInfo(ctx, session, routeIdentifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get route basic info: %w", err)
	}

	// 2. 获取最近统计数据
	recentStats, err := s.getRouteRecentStats(ctx, session, basicInfo.RouteName)
	if err != nil {
		return nil, fmt.Errorf("failed to get route recent stats: %w", err)
	}

	// 3. 获取历史统计数据（如果需要）
	var historicalStats []RouteStatistics
	if includeHistory && len(timeQuery.MonthNodes) > 1 {
		historicalStats, _ = s.getRouteHistoricalStats(ctx, session, basicInfo.RouteName, timeQuery.MonthNodes)
	}

	// 4. 获取货物构成（如果需要）
	var cargoBreakdown []RouteCargoBreakdown
	if includeCargoBreakdown {
		period := timeQuery.MonthNodes[0]
		if len(timeQuery.MonthNodes) > 0 {
			cargoBreakdown, _ = s.getRouteCargoBreakdown(ctx, session, basicInfo.RouteName, period)
		}
	}

	// 5. 获取船舶分析（如果需要）
	var topShips []RouteShipAnalysis
	if includeShipAnalysis {
		period := timeQuery.MonthNodes[0]
		if len(timeQuery.MonthNodes) > 0 {
			topShips, _ = s.getRouteTopShips(ctx, session, basicInfo.RouteName, period, 10)
		}
	}

	// 6. 生成摘要
	summary := s.generateRouteProfileSummary(historicalStats, cargoBreakdown)

	return &RouteProfileData{
		BasicInfo:       *basicInfo,
		RecentStats:     *recentStats,
		HistoricalStats: historicalStats,
		CargoBreakdown:  cargoBreakdown,
		TopShips:        topShips,
		Summary:         *summary,
	}, nil
}

// getRouteBasicInfo 获取航线基本信息
func (s *RouteService) getRouteBasicInfo(ctx context.Context, session neo4j.SessionWithContext, routeIdentifier string) (*RouteDetailInfo, error) {
	query := `
		MATCH (sr:ShippingRoute)
		WHERE sr.routeName = $identifier OR sr.routeId = $identifier
		OPTIONAL MATCH (sr)-[:ROUTE_ORIGIN]->(pO:Port)
		OPTIONAL MATCH (sr)-[:ROUTE_DESTINATION]->(pD:Port)
		RETURN sr.routeId as routeId, sr.routeName as routeName, sr.routeCode as routeCode,
		       pO.name as originPort, pD.name as destinationPort, sr.distance_km as distance,
		       sr.routeType as routeType, sr.navigationLevel as navigationLevel, sr.isActive as isActive
		LIMIT 1
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{"identifier": routeIdentifier})
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, fmt.Errorf("route not found: %s", routeIdentifier)
		}

		record := records.Record()
		return &RouteDetailInfo{
			RouteID:             getStringValue(record.Values[0]),
			RouteName:           getStringValue(record.Values[1]),
			RouteCode:           getStringValue(record.Values[2]),
			OriginPortName:      getStringValue(record.Values[3]),
			DestinationPortName: getStringValue(record.Values[4]),
			DistanceKm:          getFloatValue(record.Values[5]),
			RouteType:           getStringValue(record.Values[6]),
			NavigationLevel:     getStringValue(record.Values[7]),
			IsActive:            getBoolValue(record.Values[8]),
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*RouteDetailInfo), nil
}

// getRouteRecentStats 获取航线最近统计数据
func (s *RouteService) getRouteRecentStats(ctx context.Context, session neo4j.SessionWithContext, routeName string) (*RouteStatistics, error) {
	query := `
		MATCH (sr:ShippingRoute {routeName: $routeName})<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth)
		RETURN ym.ym as period, rms.totalShipCount as totalShipCount, rms.totalVoyageCount as totalVoyageCount,
		       rms.totalCargo_ton as totalCargoTon, rms.totalTurnover_tonkm as totalTurnoverTonKm,
		       rms.avgLoadRatio as avgLoadRatio, rms.avgVoyageTime_hours as avgVoyageTimeHours,
		       rms.utilizationRate as utilizationRate
		ORDER BY ym.ym DESC
		LIMIT 1
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{"routeName": routeName})
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return &RouteStatistics{Period: "无数据"}, nil
		}

		record := records.Record()
		return &RouteStatistics{
			Period:             getStringValue(record.Values[0]),
			TotalShipCount:     getIntValue(record.Values[1]),
			TotalVoyageCount:   getIntValue(record.Values[2]),
			TotalCargoTon:      getFloatValue(record.Values[3]),
			TotalTurnoverTonKm: getFloatValue(record.Values[4]),
			AvgLoadRatio:       getFloatValue(record.Values[5]),
			AvgVoyageTimeHours: getFloatValue(record.Values[6]),
			UtilizationRate:    getFloatValue(record.Values[7]),
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*RouteStatistics), nil
}

// getRouteHistoricalStats 获取航线历史统计数据
func (s *RouteService) getRouteHistoricalStats(ctx context.Context, session neo4j.SessionWithContext, routeName string, periods []string) ([]RouteStatistics, error) {
	query := `
		MATCH (sr:ShippingRoute {routeName: $routeName})<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth)
		WHERE ym.ym IN $periods
		RETURN ym.ym as period, rms.totalShipCount as totalShipCount, rms.totalVoyageCount as totalVoyageCount,
		       rms.totalCargo_ton as totalCargoTon, rms.totalTurnover_tonkm as totalTurnoverTonKm,
		       rms.avgLoadRatio as avgLoadRatio, rms.avgVoyageTime_hours as avgVoyageTimeHours,
		       rms.utilizationRate as utilizationRate
		ORDER BY ym.ym
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{
			"routeName": routeName,
			"periods":   periods,
		})
		if err != nil {
			return nil, err
		}

		var stats []RouteStatistics
		for records.Next(ctx) {
			record := records.Record()
			stats = append(stats, RouteStatistics{
				Period:             getStringValue(record.Values[0]),
				TotalShipCount:     getIntValue(record.Values[1]),
				TotalVoyageCount:   getIntValue(record.Values[2]),
				TotalCargoTon:      getFloatValue(record.Values[3]),
				TotalTurnoverTonKm: getFloatValue(record.Values[4]),
				AvgLoadRatio:       getFloatValue(record.Values[5]),
				AvgVoyageTimeHours: getFloatValue(record.Values[6]),
				UtilizationRate:    getFloatValue(record.Values[7]),
			})
		}

		return stats, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]RouteStatistics), nil
}

// getRouteCargoBreakdown 获取航线货物构成
func (s *RouteService) getRouteCargoBreakdown(ctx context.Context, session neo4j.SessionWithContext, routeName string, period string) ([]RouteCargoBreakdown, error) {
	query := `
		MATCH (sr:ShippingRoute {routeName: $routeName})<-[:ROUTE_CARGO_STAT_FOR_ROUTE]-(rmcs:RouteMonthCargoStat)-[:ROUTE_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
		MATCH (rmcs)-[:ROUTE_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
		RETURN ct.subName as cargoType, rmcs.cargo_ton as cargoTon, rmcs.shipCount as shipCount,
		       rmcs.voyageCount as voyageCount, rmcs.avgCargoPerVoyage_ton as avgCargoPerVoyageTon
		ORDER BY rmcs.cargo_ton DESC
		limit 3
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{
			"routeName": routeName,
			"period":    period,
		})
		if err != nil {
			return nil, err
		}

		var breakdown []RouteCargoBreakdown
		var totalCargo float64

		for records.Next(ctx) {
			record := records.Record()
			cargoTon := getFloatValue(record.Values[1])
			breakdown = append(breakdown, RouteCargoBreakdown{
				CargoType:            getStringValue(record.Values[0]),
				CargoTon:             cargoTon,
				ShipCount:            getIntValue(record.Values[2]),
				VoyageCount:          getIntValue(record.Values[3]),
				AvgCargoPerVoyageTon: getFloatValue(record.Values[4]),
			})
			totalCargo += cargoTon
		}

		// 计算百分比
		for i := range breakdown {
			if totalCargo > 0 {
				breakdown[i].Percentage = breakdown[i].CargoTon / totalCargo * 100
			}
		}

		return breakdown, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]RouteCargoBreakdown), nil
}

// getRouteTopShips 获取航线TOP船舶
func (s *RouteService) getRouteTopShips(ctx context.Context, session neo4j.SessionWithContext, routeName string, period string, limit int) ([]RouteShipAnalysis, error) {
	query := `
		MATCH (sr:ShippingRoute {routeName: $routeName})<-[:STAT_FOR_ROUTE]-(smls:ShipMonthLineStat)-[:LINE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
		MATCH (smls)-[:LINE_STAT_FOR_SHIP]->(s:Ship)
		RETURN s.name as shipName, s.mmsi as mmsi, smls.voyageCount as voyageCount,
		       smls.cargo_ton as totalCargoTon, smls.avgLoadRatio as avgLoadRatio,
		       CASE WHEN smls.voyageCount > 0 THEN smls.cargo_ton / smls.voyageCount ELSE 0 END as avgCargoPerTrip
		ORDER BY smls.cargo_ton DESC
		LIMIT $limit
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{
			"routeName": routeName,
			"period":    period,
			"limit":     limit,
		})
		if err != nil {
			return nil, err
		}

		var ships []RouteShipAnalysis
		for records.Next(ctx) {
			record := records.Record()
			ships = append(ships, RouteShipAnalysis{
				ShipName:        getStringValue(record.Values[0]),
				MMSI:            getStringValue(record.Values[1]),
				VoyageCount:     getIntValue(record.Values[2]),
				TotalCargoTon:   getFloatValue(record.Values[3]),
				AvgLoadRatio:    getFloatValue(record.Values[4]),
				AvgCargoPerTrip: getFloatValue(record.Values[5]),
			})
		}

		return ships, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]RouteShipAnalysis), nil
}

// generateRouteProfileSummary 生成航线画像摘要
func (s *RouteService) generateRouteProfileSummary(historical []RouteStatistics, cargo []RouteCargoBreakdown) *RouteProfileSummary {
	summary := &RouteProfileSummary{
		TotalMonthsData: len(historical),
	}

	// 计算平均月度货运量和船舶数
	if len(historical) > 0 {
		var totalCargo float64
		var totalShips int
		for _, h := range historical {
			totalCargo += h.TotalCargoTon
			totalShips += h.TotalShipCount
		}
		summary.AvgMonthlyCargoTon = totalCargo / float64(len(historical))
		summary.AvgMonthlyShipCount = totalShips / len(historical)
	}

	// 提取主要货物类型（前3个）
	if len(cargo) > 0 {
		maxTypes := 3
		if len(cargo) < maxTypes {
			maxTypes = len(cargo)
		}
		for i := 0; i < maxTypes; i++ {
			summary.PrimaryCargoTypes = append(summary.PrimaryCargoTypes, cargo[i].CargoType)
		}
	}

	// 性能评级
	if summary.AvgMonthlyCargoTon > 100000 {
		summary.PerformanceRating = "优秀"
	} else if summary.AvgMonthlyCargoTon > 50000 {
		summary.PerformanceRating = "良好"
	} else if summary.AvgMonthlyCargoTon > 10000 {
		summary.PerformanceRating = "一般"
	} else {
		summary.PerformanceRating = "待改善"
	}

	// 效率等级
	if summary.AvgMonthlyShipCount > 0 {
		efficiency := summary.AvgMonthlyCargoTon / float64(summary.AvgMonthlyShipCount)
		if efficiency > 5000 {
			summary.EfficiencyLevel = "高效"
		} else if efficiency > 2000 {
			summary.EfficiencyLevel = "中等"
		} else {
			summary.EfficiencyLevel = "低效"
		}
	} else {
		summary.EfficiencyLevel = "无数据"
	}

	return summary
}

// 辅助函数
func getBoolValue(value interface{}) bool {
	if value == nil {
		return false
	}
	if b, ok := value.(bool); ok {
		return b
	}
	return false
}
