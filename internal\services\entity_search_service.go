package services

import (
	"context"
	"fmt"
	"log"
	"strings"

	"neoapi/internal/models"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

// EntitySearchService 实体搜索服务
type EntitySearchService struct {
	driver neo4j.DriverWithContext
}

// NewEntitySearchService 创建新的实体搜索服务
func NewEntitySearchService(driver neo4j.DriverWithContext) *EntitySearchService {
	return &EntitySearchService{
		driver: driver,
	}
}

// SearchRequest 搜索请求
type SearchRequest struct {
	Query      string                    `json:"query" binding:"required"`
	EntityType models.EntityType         `json:"entity_type,omitempty"`
	Strategy   models.ResolutionStrategy `json:"strategy,omitempty"`
	Limit      int                       `json:"limit,omitempty"`
}

// SearchResult 搜索结果
type SearchResult struct {
	EntityType  models.EntityType `json:"entity_type"`
	Identifier  string            `json:"identifier"`
	DisplayName string            `json:"display_name"`
	Description string            `json:"description,omitempty"`
	Score       float64           `json:"score"`
	MatchType   string            `json:"match_type"` // exact, fuzzy, partial
}

// SearchResponse 搜索响应
type SearchResponse struct {
	Query   string         `json:"query"`
	Results []SearchResult `json:"results"`
	Total   int            `json:"total"`
}

// SearchEntities 搜索实体
func (s *EntitySearchService) SearchEntities(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	if req.Limit <= 0 {
		req.Limit = 10
	}

	var results []SearchResult

	// 如果指定了实体类型，只搜索该类型
	if req.EntityType != "" {
		typeResults, err := s.searchByEntityType(ctx, req.Query, req.EntityType, req.Strategy, req.Limit)
		if err != nil {
			return nil, err
		}
		results = append(results, typeResults...)
	} else {
		// 搜索所有实体类型
		allTypes := []models.EntityType{
			models.EntityTypeShip,
			models.EntityTypePort,
			models.EntityTypeProvince,
			models.EntityTypeBasin,
			models.EntityTypeShippingRoute,
		}

		limitPerType := req.Limit / len(allTypes)
		if limitPerType < 1 {
			limitPerType = 1
		}

		for _, entityType := range allTypes {
			typeResults, err := s.searchByEntityType(ctx, req.Query, entityType, req.Strategy, limitPerType)
			if err != nil {
				continue // 跳过错误的类型
			}
			results = append(results, typeResults...)
		}

		// 按分数排序并限制结果数量
		results = s.sortAndLimitResults(results, req.Limit)
	}

	return &SearchResponse{
		Query:   req.Query,
		Results: results,
		Total:   len(results),
	}, nil
}

// searchByEntityType 按实体类型搜索
func (s *EntitySearchService) searchByEntityType(ctx context.Context, query string, entityType models.EntityType, strategy models.ResolutionStrategy, limit int) ([]SearchResult, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	switch entityType {
	case models.EntityTypeShip:
		return s.searchShips(ctx, session, query, strategy, limit)
	case models.EntityTypePort:
		return s.searchPorts(ctx, session, query, strategy, limit)
	case models.EntityTypeProvince:
		return s.searchProvinces(ctx, session, query, strategy, limit)
	case models.EntityTypeBasin:
		return s.searchBasins(ctx, session, query, strategy, limit)
	case models.EntityTypeShippingRoute:
		return s.searchShippingRoutes(ctx, session, query, strategy, limit)
	default:
		return nil, fmt.Errorf("unsupported entity type: %s", entityType)
	}
}

// searchShips 搜索船舶
func (s *EntitySearchService) searchShips(ctx context.Context, session neo4j.SessionWithContext, query string, strategy models.ResolutionStrategy, limit int) ([]SearchResult, error) {
	var cypher string
	var params map[string]interface{}

	if strategy == models.ResolutionExact {
		// 精确匹配MMSI或船名
		cypher = `
			MATCH (s:Ship)
			WHERE s.mmsi = $query OR s.name = $query
			RETURN s.mmsi as mmsi, s.name as name, s.owner as owner, s.dwt as dwt
			LIMIT $limit
		`
		params = map[string]interface{}{
			"query": query,
			"limit": limit,
		}
	} else {
		// 模糊匹配
		cypher = `
			MATCH (s:Ship)
			WHERE toLower(s.name) CONTAINS toLower($query) OR s.mmsi CONTAINS $query
			RETURN s.mmsi as mmsi, s.name as name, s.owner as owner, s.dwt as dwt
			ORDER BY 
				CASE 
					WHEN s.name = $query THEN 1
					WHEN s.mmsi = $query THEN 2
					WHEN toLower(s.name) STARTS WITH toLower($query) THEN 3
					WHEN s.mmsi STARTS WITH $query THEN 4
					ELSE 5
				END
			LIMIT $limit
		`
		params = map[string]interface{}{
			"query": query,
			"limit": limit,
		}
	}

	// 打印Cypher查询日志
	log.Printf("[SearchShips] Executing Cypher query:\n%s\nParams: %+v", cypher, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, cypher, params)
		if err != nil {
			log.Printf("[SearchShips] Cypher query failed: %v", err)
			return nil, err
		}

		var results []SearchResult
		for records.Next(ctx) {
			record := records.Record()
			mmsi := getStringValue(record.Values[0])
			name := getStringValue(record.Values[1])
			owner := getStringValue(record.Values[2])
			dwt := getFloatValue(record.Values[3])

			// 计算匹配分数
			score := s.calculateShipMatchScore(query, mmsi, name)
			matchType := s.getMatchType(query, name, mmsi)

			description := fmt.Sprintf("MMSI: %s", mmsi)
			if owner != "" {
				description += fmt.Sprintf(", 船东: %s", owner)
			}
			if dwt > 0 {
				description += fmt.Sprintf(", 载重吨: %.0f", dwt)
			}

			results = append(results, SearchResult{
				EntityType:  models.EntityTypeShip,
				Identifier:  mmsi,
				DisplayName: name,
				Description: description,
				Score:       score,
				MatchType:   matchType,
			})
		}

		return results, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]SearchResult), nil
}

// searchPorts 搜索港口
func (s *EntitySearchService) searchPorts(ctx context.Context, session neo4j.SessionWithContext, query string, strategy models.ResolutionStrategy, limit int) ([]SearchResult, error) {
	var cypher string
	var params map[string]interface{}

	if strategy == models.ResolutionExact {
		cypher = `
			MATCH (p:Port)
			WHERE p.name = $query
			RETURN p.name as name, p.prov as prov
			LIMIT $limit
		`
	} else {
		cypher = `
			MATCH (p:Port)
			WHERE toLower(p.name) CONTAINS toLower($query)
			RETURN p.name as name, p.prov as prov
			ORDER BY 
				CASE 
					WHEN p.name = $query THEN 1
					WHEN toLower(p.name) STARTS WITH toLower($query) THEN 2
					ELSE 3
				END
			LIMIT $limit
		`
	}

	params = map[string]interface{}{
		"query": query,
		"limit": limit,
	}

	// 打印Cypher查询日志
	log.Printf("[searchPorts] Executing Cypher query:\n%s\nParams: %+v", cypher, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, cypher, params)
		if err != nil {
			log.Printf("[searchPorts] Cypher query failed: %v", err)
			return nil, err
		}

		var results []SearchResult
		for records.Next(ctx) {
			record := records.Record()
			name := getStringValue(record.Values[0])
			prov := getStringValue(record.Values[1])

			score := s.calculateStringMatchScore(query, name)
			matchType := s.getMatchType(query, name, "")

			description := ""
			if prov != "" {
				description = fmt.Sprintf("省份: %s", prov)
			}

			results = append(results, SearchResult{
				EntityType:  models.EntityTypePort,
				Identifier:  name,
				DisplayName: name,
				Description: description,
				Score:       score,
				MatchType:   matchType,
			})
		}

		return results, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]SearchResult), nil
}

// searchProvinces 搜索省份
func (s *EntitySearchService) searchProvinces(ctx context.Context, session neo4j.SessionWithContext, query string, strategy models.ResolutionStrategy, limit int) ([]SearchResult, error) {
	cypher := `
		MATCH (p:Province)
		WHERE toLower(p.name) CONTAINS toLower($query)
		RETURN p.name as name, p.code as code
		ORDER BY 
			CASE 
				WHEN p.name = $query THEN 1
				WHEN toLower(p.name) STARTS WITH toLower($query) THEN 2
				ELSE 3
			END
		LIMIT $limit
	`

	params := map[string]interface{}{
		"query": query,
		"limit": limit,
	}

	// 打印Cypher查询日志
	log.Printf("[searchProvinces] Executing Cypher query:\n%s\nParams: %+v", cypher, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, cypher, params)
		if err != nil {
			log.Printf("[searchProvinces] Cypher query failed: %v", err)
			return nil, err
		}

		var results []SearchResult
		for records.Next(ctx) {
			record := records.Record()
			name := getStringValue(record.Values[0])
			code := getStringValue(record.Values[1])

			score := s.calculateStringMatchScore(query, name)
			matchType := s.getMatchType(query, name, "")

			description := ""
			if code != "" {
				description = fmt.Sprintf("代码: %s", code)
			}

			results = append(results, SearchResult{
				EntityType:  models.EntityTypeProvince,
				Identifier:  name,
				DisplayName: name,
				Description: description,
				Score:       score,
				MatchType:   matchType,
			})
		}

		return results, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]SearchResult), nil
}

// searchBasins 搜索流域
func (s *EntitySearchService) searchBasins(ctx context.Context, session neo4j.SessionWithContext, query string, strategy models.ResolutionStrategy, limit int) ([]SearchResult, error) {
	cypher := `
		MATCH (b:Basin)
		WHERE toLower(b.name) CONTAINS toLower($query)
		RETURN b.name as name
		ORDER BY 
			CASE 
				WHEN b.name = $query THEN 1
				WHEN toLower(b.name) STARTS WITH toLower($query) THEN 2
				ELSE 3
			END
		LIMIT $limit
	`

	params := map[string]interface{}{
		"query": query,
		"limit": limit,
	}

	// 打印Cypher查询日志
	log.Printf("[searchBasins] Executing Cypher query:\n%s\nParams: %+v", cypher, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, cypher, params)
		if err != nil {
			log.Printf("[searchBasins] Cypher query failed: %v", err)
			return nil, err
		}

		var results []SearchResult
		for records.Next(ctx) {
			record := records.Record()
			name := getStringValue(record.Values[0])

			score := s.calculateStringMatchScore(query, name)
			matchType := s.getMatchType(query, name, "")

			results = append(results, SearchResult{
				EntityType:  models.EntityTypeBasin,
				Identifier:  name,
				DisplayName: name,
				Description: "流域",
				Score:       score,
				MatchType:   matchType,
			})
		}

		return results, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]SearchResult), nil
}

// searchShippingRoutes 搜索航线
func (s *EntitySearchService) searchShippingRoutes(ctx context.Context, session neo4j.SessionWithContext, query string, strategy models.ResolutionStrategy, limit int) ([]SearchResult, error) {
	cypher := `
		MATCH (sr:ShippingRoute)
		WHERE toLower(sr.routeName) CONTAINS toLower($query) OR sr.routeId = $query
		OPTIONAL MATCH (sr)-[:ROUTE_ORIGIN]->(pO:Port)
		OPTIONAL MATCH (sr)-[:ROUTE_DESTINATION]->(pD:Port)
		RETURN sr.routeId as routeId, sr.routeName as routeName, 
		       pO.name as originPort, pD.name as destinationPort, sr.distance_km as distance
		ORDER BY 
			CASE 
				WHEN sr.routeName = $query THEN 1
				WHEN sr.routeId = $query THEN 2
				WHEN toLower(sr.routeName) STARTS WITH toLower($query) THEN 3
				ELSE 4
			END
		LIMIT $limit
	`

	params := map[string]interface{}{
		"query": query,
		"limit": limit,
	}

	// 打印Cypher查询日志
	log.Printf("[searchShippingRoutes] Executing Cypher query:\n%s\nParams: %+v", cypher, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, cypher, params)
		if err != nil {
			log.Printf("[searchShippingRoutes] Cypher query failed: %v", err)
			return nil, err
		}

		var results []SearchResult
		for records.Next(ctx) {
			record := records.Record()
			routeId := getStringValue(record.Values[0])
			routeName := getStringValue(record.Values[1])
			originPort := getStringValue(record.Values[2])
			destinationPort := getStringValue(record.Values[3])
			distance := getFloatValue(record.Values[4])

			score := s.calculateStringMatchScore(query, routeName)
			matchType := s.getMatchType(query, routeName, routeId)

			description := fmt.Sprintf("航线ID: %s", routeId)
			if originPort != "" && destinationPort != "" {
				description += fmt.Sprintf(", %s → %s", originPort, destinationPort)
			}
			if distance > 0 {
				description += fmt.Sprintf(", 距离: %.1f公里", distance)
			}

			results = append(results, SearchResult{
				EntityType:  models.EntityTypeShippingRoute,
				Identifier:  routeId,
				DisplayName: routeName,
				Description: description,
				Score:       score,
				MatchType:   matchType,
			})
		}

		return results, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]SearchResult), nil
}

// 辅助函数
func (s *EntitySearchService) calculateShipMatchScore(query, mmsi, name string) float64 {
	query = strings.ToLower(query)
	mmsi = strings.ToLower(mmsi)
	name = strings.ToLower(name)

	if mmsi == query || name == query {
		return 1.0
	}
	if strings.HasPrefix(mmsi, query) || strings.HasPrefix(name, query) {
		return 0.8
	}
	if strings.Contains(mmsi, query) || strings.Contains(name, query) {
		return 0.6
	}
	return 0.3
}

func (s *EntitySearchService) calculateStringMatchScore(query, target string) float64 {
	query = strings.ToLower(query)
	target = strings.ToLower(target)

	if target == query {
		return 1.0
	}
	if strings.HasPrefix(target, query) {
		return 0.8
	}
	if strings.Contains(target, query) {
		return 0.6
	}
	return 0.3
}

func (s *EntitySearchService) getMatchType(query, primary, secondary string) string {
	query = strings.ToLower(query)
	primary = strings.ToLower(primary)
	secondary = strings.ToLower(secondary)

	if primary == query || secondary == query {
		return "exact"
	}
	if strings.HasPrefix(primary, query) || strings.HasPrefix(secondary, query) {
		return "prefix"
	}
	if strings.Contains(primary, query) || strings.Contains(secondary, query) {
		return "partial"
	}
	return "fuzzy"
}

func (s *EntitySearchService) sortAndLimitResults(results []SearchResult, limit int) []SearchResult {
	// 按分数降序排序
	for i := 0; i < len(results)-1; i++ {
		for j := i + 1; j < len(results); j++ {
			if results[i].Score < results[j].Score {
				results[i], results[j] = results[j], results[i]
			}
		}
	}

	// 限制结果数量
	if len(results) > limit {
		results = results[:limit]
	}

	return results
}
