package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"neoapi/internal/models"
	timeparser "neoapi/internal/time"
	"neoapi/internal/utils"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

// QueryService 统一查询服务
type QueryService struct {
	driver        neo4j.DriverWithContext
	timeParser    *timeparser.CSTELParser
	metricService *MetricService
	routeService  *RouteService
}

// NewQueryService 创建新的查询服务
func NewQueryService(driver neo4j.DriverWithContext) *QueryService {
	return &QueryService{
		driver:        driver,
		timeParser:    timeparser.NewCSTELParser(),
		metricService: NewMetricService(),
		routeService:  NewRouteService(driver),
	}
}

// ExecuteQuery 执行统一查询
func (s *QueryService) ExecuteQuery(ctx context.Context, req *models.UnifiedQueryRequest) (*models.UnifiedQueryResponse, error) {
	startTime := time.Now()

	log.Printf("[ExecuteQuery] Starting query - Type: %s, Entity: %+v, Metric: %s, TimeExpression: %s",
		req.QueryType, req.Entity, req.Metric, req.TimeExpression)

	// 解析时间表达式
	timeQuery, err := s.timeParser.Parse(req.TimeExpression)
	if err != nil {
		log.Printf("[ExecuteQuery] Time expression parsing failed - expression: '%s', error: %v", req.TimeExpression, err)
		return &models.UnifiedQueryResponse{
			Status: "error",
			Error:  fmt.Sprintf("Invalid time expression: %v", err),
		}, nil
	}

	log.Printf("[ExecuteQuery] Time expression parsed successfully - periods: %v", timeQuery.MonthNodes)

	// 对于比较查询和排名查询，不需要解析单个实体
	var entityInfo *models.EntityInfo
	if req.QueryType != models.QueryTypeCompare && req.QueryType != models.QueryTypeRank {
		// 解析实体标识符
		var err error
		entityInfo, err = s.resolveEntity(ctx, &req.Entity)
		if err != nil {
			log.Printf("[ExecuteQuery] Entity resolution failed - entity: %+v, error: %v", req.Entity, err)
			return &models.UnifiedQueryResponse{
				Status: "error",
				Error:  fmt.Sprintf("Entity resolution failed: %v", err),
			}, nil
		}
		log.Printf("[ExecuteQuery] Entity resolved successfully - entityInfo: %+v", entityInfo)
	} else {
		log.Printf("[ExecuteQuery] Skipping entity resolution for %s query", req.QueryType)
	}

	// 优先检查是否有过滤器，如果有则降级处理（优先级高于空metric处理）
	if utils.HasFilters(req.Filters) {
		log.Printf("[ExecuteQuery] Filters detected, switching to filtered dimension query")
		data, err := s.executeFilteredDimensionQuery(ctx, entityInfo, req.Filters, timeQuery)
		if err != nil {
			return &models.UnifiedQueryResponse{
				Status: "error",
				Error:  err.Error(),
			}, nil
		}

		return &models.UnifiedQueryResponse{
			Entity: func() string {
				if identifier, ok := req.Entity.Identifier.(string); ok {
					return identifier
				}
				return ""
			}(),
			Metric: req.Metric, // 保持原有的metric字段
			Period: req.TimeExpression,
			Data:   data,
			Status: "success",
		}, nil
	}

	// 处理空metric的情况 - 但COMPARE和PROFILE查询需要特殊处理
	if req.Metric == "" && req.QueryType != models.QueryTypeCompare && req.QueryType != models.QueryTypeProfile {
		log.Printf("[ExecuteQuery] Empty metric detected, returning all metrics for entity type: %s", req.Entity.Type)
		return s.executeQueryForAllMetrics(ctx, req, timeQuery)
	}

	// 检查是否为复合指标
	metricConfig, err := s.metricService.GetMetricConfig(req.Metric)
	if err == nil && metricConfig.IsComposite {
		log.Printf("[ExecuteQuery] Processing composite metric: %s", req.Metric)
		compositeData := s.executeCompositeMetricQuery(ctx, req, timeQuery, metricConfig)
		return &models.UnifiedQueryResponse{
			Metric: req.Metric,
			Data:   compositeData,
			Status: "success",
			Entity: func() string {
				if identifier, ok := req.Entity.Identifier.(string); ok {
					return identifier
				}
				return ""
			}(),
			Entities: func() []string {
				if identifiers, isList := req.Entity.Identifier.([]interface{}); isList {
					var entityNames []string
					for _, id := range identifiers {
						if name, ok := id.(string); ok {
							entityNames = append(entityNames, name)
						}
					}
					return entityNames
				}
				return nil
			}(),
			Period: req.TimeExpression,
		}, nil
	}

	// 根据查询类型执行相应的查询
	var data interface{}
	switch req.QueryType {
	case models.QueryTypePoint:
		data, err = s.executePointQuery(ctx, entityInfo, req.Metric, timeQuery, req.TimeExpression)
	case models.QueryTypeProfile:
		data, err = s.executeProfileQuery(ctx, entityInfo, timeQuery, nil)
	case models.QueryTypeTrend:
		data, err = s.executeTrendQuery(ctx, entityInfo, req.Metric, timeQuery)
	case models.QueryTypeCompare:
		// 检查是否为综合比较查询（空metric或"全部指标"且无filter）
		hasFiltersResult := utils.HasFilters(req.Filters)
		isComprehensive := (req.Metric == "" || req.Metric == "全部指标") && !hasFiltersResult
		log.Printf("[ExecuteQuery] COMPARE query analysis - metric: '%s', hasFilters: %v, isComprehensive: %v", req.Metric, hasFiltersResult, isComprehensive)

		if isComprehensive {
			log.Printf("[ExecuteQuery] Comprehensive comparison triggered - metric: '%s', filters: %v", req.Metric, req.Filters)
			data, err = s.executeComprehensiveCompareQuery(ctx, &req.Entity, timeQuery)
		} else {
			log.Printf("[ExecuteQuery] Executing precise compare query with metric: %s", req.Metric)
			data, err = s.executeCompareQuery(ctx, &req.Entity, req.Metric, timeQuery, req.TimeExpression)
		}
	case models.QueryTypeRank:
		data, err = s.executeRankQuery(ctx, &req.Entity, req.Metric, timeQuery, req.Limit)
	case models.QueryTypeCompose:
		data, err = s.executeComposeQuery(ctx, entityInfo, req.Metric, timeQuery, nil)
	default:
		return &models.UnifiedQueryResponse{
			Status: "error",
			Error:  fmt.Sprintf("Unsupported query type: %s", req.QueryType),
		}, nil
	}

	if err != nil {
		response := &models.UnifiedQueryResponse{
			Metric: req.Metric,
			Period: req.TimeExpression,
			Status: "error",
			Error:  err.Error(),
		}

		// 设置实体信息（错误情况下）
		if identifiers, isList := req.Entity.Identifier.([]interface{}); isList {
			var entityNames []string
			for _, id := range identifiers {
				if name, ok := id.(string); ok {
					entityNames = append(entityNames, name)
				}
			}
			response.Entities = entityNames
		} else if identifier, ok := req.Entity.Identifier.(string); ok {
			response.Entity = identifier
		}

		// 打印错误响应的JSON
		if jsonBytes, err := json.MarshalIndent(response, "", "  "); err == nil {
			log.Printf("[ExecuteQuery] Error Response JSON:\n%s", string(jsonBytes))
		}

		return response, nil
	}

	// 构建新的统一响应格式
	response := &models.UnifiedQueryResponse{
		Metric: req.Metric,
		Data:   data,
		Status: "success",
	}

	// 设置实体信息
	if identifiers, isList := req.Entity.Identifier.([]interface{}); isList {
		// 多个实体（COMPARE查询）
		var entityNames []string
		for _, id := range identifiers {
			if name, ok := id.(string); ok {
				entityNames = append(entityNames, name)
			}
		}
		response.Entities = entityNames
	} else if identifier, ok := req.Entity.Identifier.(string); ok {
		// 单个实体
		response.Entity = identifier
	}

	// 设置时间段
	if len(timeQuery.MonthNodes) > 0 {
		if len(timeQuery.MonthNodes) == 1 {
			response.Period = timeQuery.MonthNodes[0]
		} else {
			response.Period = req.TimeExpression // 使用原始表达式
		}
	} else {
		response.Period = req.TimeExpression
	}

	log.Printf("[ExecuteQuery] Query completed successfully - Type: %s, Duration: %v", req.QueryType, time.Since(startTime))

	// 打印格式化的JSON响应结果
	// if jsonBytes, err := json.MarshalIndent(response, "", "  "); err == nil {
	// 	log.Printf("[ExecuteQuery] Response JSON:\n%s", string(jsonBytes))
	// } else {
	// 	log.Printf("[ExecuteQuery] Failed to marshal response to JSON: %v", err)
	// }

	return response, nil
}

// resolveEntity 解析实体标识符
func (s *QueryService) resolveEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	switch entityReq.Type {
	case models.EntityTypeShip:
		return s.resolveShipEntity(ctx, entityReq)
	case models.EntityTypePort:
		return s.resolvePortEntity(ctx, entityReq)
	case models.EntityTypeProvince:
		return s.resolveProvinceEntity(ctx, entityReq)
	case models.EntityTypeBasin:
		return s.resolveBasinEntity(ctx, entityReq)
	case models.EntityTypeShippingRoute:
		return s.resolveShippingRouteEntity(ctx, entityReq)
	default:
		return nil, fmt.Errorf("unsupported entity type: %s", entityReq.Type)
	}
}

// resolveShipEntity 解析船舶实体
func (s *QueryService) resolveShipEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	// 支持字符串和字符串数组
	var identifier string
	switch v := entityReq.Identifier.(type) {
	case string:
		identifier = v
	case []interface{}:
		if len(v) > 0 {
			if str, ok := v[0].(string); ok {
				identifier = str
			} else {
				return nil, fmt.Errorf("ship identifier array must contain strings")
			}
		} else {
			return nil, fmt.Errorf("ship identifier array cannot be empty")
		}
	case []string:
		if len(v) > 0 {
			identifier = v[0]
		} else {
			return nil, fmt.Errorf("ship identifier array cannot be empty")
		}
	default:
		return nil, fmt.Errorf("ship identifier must be a string or string array, got %T", entityReq.Identifier)
	}

	log.Printf("[resolveShipEntity] Resolving ship identifier: '%s'", identifier)

	var query string
	var params map[string]interface{}

	if entityReq.ResolutionStrategy == models.ResolutionExact {
		// 精确匹配MMSI
		query = `MATCH (s:Ship {mmsi: $identifier}) RETURN s.mmsi as mmsi, s.name as name, s.shipId as shipId`
		params = map[string]interface{}{"identifier": identifier}
	} else {
		// 模糊匹配船名或MMSI
		query = `
			MATCH (s:Ship)
			WHERE toLower(s.name) CONTAINS toLower($identifier) OR s.mmsi CONTAINS $identifier
			RETURN s.mmsi as mmsi, s.name as name, s.shipId as shipId
			LIMIT 1
		`
		params = map[string]interface{}{"identifier": identifier}
	}

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, fmt.Errorf("ship not found: %s", identifier)
		}

		record := records.Record()
		return &models.EntityInfo{
			Type:             models.EntityTypeShip,
			Name:             record.Values[1].(string),
			MMSI:             record.Values[0].(string),
			ShipID:           record.Values[2].(string),
			ResolutionMethod: string(entityReq.ResolutionStrategy),
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.EntityInfo), nil
}

// resolvePortEntity 解析港口实体
func (s *QueryService) resolvePortEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	// 支持字符串和字符串数组
	var identifier string
	switch v := entityReq.Identifier.(type) {
	case string:
		identifier = v
	case []interface{}:
		if len(v) > 0 {
			if str, ok := v[0].(string); ok {
				identifier = str
			} else {
				return nil, fmt.Errorf("port identifier array must contain strings")
			}
		} else {
			return nil, fmt.Errorf("port identifier array cannot be empty")
		}
	case []string:
		if len(v) > 0 {
			identifier = v[0]
		} else {
			return nil, fmt.Errorf("port identifier array cannot be empty")
		}
	default:
		return nil, fmt.Errorf("port identifier must be a string or string array, got %T", entityReq.Identifier)
	}

	log.Printf("[resolvePortEntity] Resolving port identifier: '%s'", identifier)

	// 去除常见的港口后缀
	cleanedName := s.removeEntitySuffixes(identifier, []string{"港", "港口", "码头"})
	log.Printf("[resolvePortEntity] Cleaned port name: '%s' -> '%s'", identifier, cleanedName)

	return &models.EntityInfo{
		Type:             models.EntityTypePort,
		Name:             cleanedName,
		ResolutionMethod: string(entityReq.ResolutionStrategy),
	}, nil
}

// resolveProvinceEntity 解析省份实体
func (s *QueryService) resolveProvinceEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	identifier, ok := entityReq.Identifier.(string)
	if !ok {
		return nil, fmt.Errorf("province identifier must be a string")
	}

	return &models.EntityInfo{
		Type:             models.EntityTypeProvince,
		Name:             identifier,
		ResolutionMethod: string(entityReq.ResolutionStrategy),
	}, nil
}

// resolveBasinEntity 解析流域实体
func (s *QueryService) resolveBasinEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	identifier, ok := entityReq.Identifier.(string)
	if !ok {
		return nil, fmt.Errorf("basin identifier must be a string")
	}

	return &models.EntityInfo{
		Type:             models.EntityTypeBasin,
		Name:             identifier,
		ResolutionMethod: string(entityReq.ResolutionStrategy),
	}, nil
}

// resolveShippingRouteEntity 解析航线实体
func (s *QueryService) resolveShippingRouteEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	identifier, ok := entityReq.Identifier.(string)
	if !ok {
		return nil, fmt.Errorf("shipping route identifier must be a string")
	}

	query := `
		MATCH (sr:ShippingRoute)
		WHERE toLower(sr.routeName) CONTAINS toLower($identifier) OR sr.routeId = $identifier
		OPTIONAL MATCH (sr)-[:ROUTE_ORIGIN]->(pO:Port)
		OPTIONAL MATCH (sr)-[:ROUTE_DESTINATION]->(pD:Port)
		RETURN sr.routeId as routeId, sr.routeName as routeName, 
		       pO.name as originPort, pD.name as destinationPort, sr.distance_km as distance
		LIMIT 1
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{"identifier": identifier}
		log.Printf("[resolveShippingRouteEntity] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, fmt.Errorf("shipping route not found: %s", identifier)
		}

		record := records.Record()
		return &models.EntityInfo{
			Type:             models.EntityTypeShippingRoute,
			RouteID:          record.Values[0].(string),
			RouteName:        record.Values[1].(string),
			OriginPort:       utils.GetStringValue(record.Values[2]),
			DestinationPort:  utils.GetStringValue(record.Values[3]),
			DistanceKm:       utils.GetFloatValue(record.Values[4]),
			ResolutionMethod: string(entityReq.ResolutionStrategy),
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.EntityInfo), nil
}

// mapMetricToDBField 将指标名称映射到数据库字段（使用MetricService）
func (s *QueryService) mapMetricToDBField(metric string) string {
	dbField, err := s.metricService.GetDBField(metric)
	if err != nil {
		log.Printf("[QueryService] Failed to map metric '%s' to DB field: %v", metric, err)
		return metric // 如果没有映射，返回原始值
	}
	return dbField
}

// getMetricUnit 获取指标单位（使用MetricService）
func (s *QueryService) getMetricUnit(metric string) string {
	unit, err := s.metricService.GetUnit(metric)
	if err != nil {
		log.Printf("[QueryService] Failed to get unit for metric '%s': %v", metric, err)
		return ""
	}
	return unit
}

// GetShipRealtimeData 获取船舶实时数据
func (s *QueryService) GetShipRealtimeData(ctx context.Context, identifier string) (*models.RealtimeData, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{AccessMode: neo4j.AccessModeRead})
	defer session.Close(ctx)

	// 查询船舶实时数据
	query := `
		MATCH (s:Ship)-[:HAS_REALTIME_STATUS]->(sr:ShipRealtime)
		WHERE s.name = $identifier OR s.mmsi = $identifier
		RETURN s.name as ship_name, s.mmsi as mmsi,
		       sr.lat as lat, sr.lon as lon, sr.sog as sog, sr.cog as cog,
		       sr.navStatus as nav_status, sr.aisTimestamp as ais_timestamp,
		       sr.km as km, sr.portAis as port_ais, sr.orgName as org_name,
		       sr.portStatus as port_status, sr.portReport as port_report,
		       sr.reportTimeIn as report_time_in, sr.reportTimeOut as report_time_out,
		       sr.actualCarryCapacityIn_ton as actual_carry_capacity_in,
		       sr.actualCarryCapacityOut_ton as actual_carry_capacity_out,
		       sr.portCargoIn_to as port_cargo_in, sr.portCargoOut_ton as port_cargo_out,
		       sr.lastUpdated as last_updated
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{
			"identifier": identifier,
		})
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, fmt.Errorf("ship not found")
		}

		record := records.Record()

		// 构建实时数据响应
		realtimeData := &models.RealtimeData{
			Entity: models.EntityInfo{
				Type: models.EntityTypeShip,
				Name: utils.GetStringValue(record.Values[0]),
				MMSI: utils.GetStringValue(record.Values[1]),
			},
			RealtimeData: map[string]interface{}{
				"position": map[string]interface{}{
					"lat":       utils.GetFloatValue(record.Values[2]),
					"lon":       utils.GetFloatValue(record.Values[3]),
					"timestamp": utils.GetStringValue(record.Values[7]),
				},
				"navigation": map[string]interface{}{
					"sog":       utils.GetFloatValue(record.Values[4]),
					"cog":       utils.GetFloatValue(record.Values[5]),
					"navStatus": utils.GetStringValue(record.Values[6]),
				},
				"location": map[string]interface{}{
					"km":      utils.GetFloatValue(record.Values[8]),
					"portAis": utils.GetStringValue(record.Values[9]),
					"orgName": utils.GetStringValue(record.Values[10]),
				},
				"port_status": map[string]interface{}{
					"status":        utils.GetStringValue(record.Values[11]),
					"report":        utils.GetStringValue(record.Values[12]),
					"reportTimeIn":  utils.GetStringValue(record.Values[13]),
					"reportTimeOut": utils.GetStringValue(record.Values[14]),
				},
				"cargo_status": map[string]interface{}{
					"carryCapacityIn":  utils.GetFloatValue(record.Values[15]),
					"carryCapacityOut": utils.GetFloatValue(record.Values[16]),
					"cargoIn":          utils.GetStringValue(record.Values[17]),
					"cargoOut":         utils.GetStringValue(record.Values[18]),
				},
			},
			Status: "success",
		}

		return realtimeData, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.RealtimeData), nil
}

// getMetricValue 获取单个指标值
func (s *QueryService) getMetricValue(ctx context.Context, entityType models.EntityType, identifier string, metric string, timeExpression string) (interface{}, string, error) {
	// 解析时间表达式
	timeQuery, err := s.timeParser.Parse(timeExpression)
	if err != nil {
		return nil, "", err
	}

	if len(timeQuery.MonthNodes) == 0 {
		return nil, "", fmt.Errorf("no time periods specified")
	}

	period := timeQuery.MonthNodes[0]
	dbField := s.mapMetricToDBField(metric)
	unit := s.getMetricUnit(metric)

	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	var params map[string]interface{}

	switch entityType {
	case models.EntityTypeShip:
		query = fmt.Sprintf(`
			MATCH (s:Ship {name: $entityName})<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			RETURN sms.%s as value
		`, dbField)
	case models.EntityTypePort:
		query = fmt.Sprintf(`
			MATCH (p:Port {name: $entityName})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			RETURN pms.%s as value
		`, dbField)
	default:
		return nil, "", fmt.Errorf("unsupported entity type: %s", entityType)
	}

	params = map[string]interface{}{
		"entityName": identifier,
		"period":     period,
	}

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, nil // 没有数据
		}

		return records.Record().Values[0], nil
	})

	if err != nil {
		return nil, unit, err
	}

	return result, unit, nil
}

// UnifiedFallback 统一降级处理 - 简单版本
func (s *QueryService) UnifiedFallback(ctx context.Context, entityType models.EntityType, entityName string, metric string, timeExpression string) *models.FallbackResult {
	log.Printf("[UnifiedFallback] Processing fallback for entity: %s, metric: %s", entityName, metric)

	// 步骤1: 尝试时间降级（去年同期）
	if fallbackData := s.tryTimeFallback(ctx, entityType, entityName, metric, timeExpression); fallbackData != nil {
		return fallbackData
	}

	// 步骤2: 检查实体是否存在并返回基本信息
	return s.tryEntityExistsFallback(ctx, entityType, entityName, metric)
}

// tryTimeFallback 尝试时间降级（去年同期）
func (s *QueryService) tryTimeFallback(ctx context.Context, entityType models.EntityType, entityName string, metric string, timeExpression string) *models.FallbackResult {
	lastYearExpr := s.convertToLastYear(timeExpression)
	if lastYearExpr == "" {
		return nil // 无法转换时间，跳过时间降级
	}

	value, unit, err := s.getMetricValue(ctx, entityType, entityName, metric, lastYearExpr)
	if err == nil && value != nil {
		return &models.FallbackResult{
			Value:        value,
			Unit:         unit,
			Status:       "time_fallback_last_year",
			Message:      "当前时间段无数据，已返回去年同期数据",
			FallbackType: "time_fallback",
			AlternativeInfo: map[string]interface{}{
				"original_time": timeExpression,
				"fallback_time": lastYearExpr,
			},
		}
	}
	return nil
}

// tryEntityExistsFallback 检查实体存在性并返回基本信息
func (s *QueryService) tryEntityExistsFallback(ctx context.Context, entityType models.EntityType, entityName string, metric string) *models.FallbackResult {
	exists, basicInfo := s.checkEntityExistsWithBasicInfo(ctx, entityType, entityName)

	if exists {
		return &models.FallbackResult{
			Value:           nil,
			Status:          "metric_unavailable_entity_exists",
			Message:         fmt.Sprintf("指标'%s'不可用，但实体存在", metric),
			FallbackType:    "entity_exists",
			AlternativeInfo: basicInfo,
		}
	}

	return &models.FallbackResult{
		Value:        nil,
		Status:       "entity_not_found",
		Message:      fmt.Sprintf("实体'%s'不存在", entityName),
		FallbackType: "not_found",
	}
}

// convertToLastYear 简单的去年时间转换
func (s *QueryService) convertToLastYear(timeExpression string) string {
	// R6M 等相对时间保持不变（系统会自动计算去年同期）
	if strings.HasPrefix(timeExpression, "R") {
		return timeExpression
	}

	// M202508 -> M202408 (月份格式转换)
	if strings.HasPrefix(timeExpression, "M") && len(timeExpression) == 7 {
		year := timeExpression[1:5]
		month := timeExpression[5:7]
		if yearInt, err := strconv.Atoi(year); err == nil {
			return fmt.Sprintf("M%04d%s", yearInt-1, month)
		}
	}

	return "" // 无法转换的格式
}

// checkEntityExistsWithBasicInfo 检查实体存在性并获取基本信息
func (s *QueryService) checkEntityExistsWithBasicInfo(ctx context.Context, entityType models.EntityType, entityName string) (bool, map[string]interface{}) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	switch entityType {
	case models.EntityTypeShip:
		query = `
			MATCH (s:Ship {name: $entityName})
			RETURN s.name as name, s.mmsi as mmsi, s.owner as owner
		`
	case models.EntityTypePort:
		query = `
			MATCH (p:Port {name: $entityName})
			OPTIONAL MATCH (p)-[:BELONGS_TO_PROVINCE]->(prov:Province)
			RETURN p.name as name, p.code as code, prov.name as province
		`
	default:
		return false, nil
	}

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{"entityName": entityName}
		log.Printf("[checkEntityExistsWithBasicInfo] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, nil // 实体不存在
		}

		record := records.Record()
		basicInfo := map[string]interface{}{
			"entity_type": string(entityType),
		}

		// 添加查询到的基本信息
		for i, key := range record.Keys {
			if record.Values[i] != nil {
				basicInfo[key] = utils.GetStringValue(record.Values[i])
			}
		}

		return basicInfo, nil
	})

	if err != nil || result == nil {
		return false, nil
	}

	return true, result.(map[string]interface{})
}

// executeCompositeMetricQuery 执行复合指标查询
func (s *QueryService) executeCompositeMetricQuery(ctx context.Context, req *models.UnifiedQueryRequest, timeQuery *timeparser.TimeQuery, metricConfig *MetricConfig) map[string]interface{} {
	log.Printf("[executeCompositeMetricQuery] Executing composite metric query for: %s", metricConfig.ChineseName)

	compositeData := make(map[string]interface{})
	compositeData["type"] = "composite"
	compositeData["components"] = make(map[string]interface{})

	// 解析实体信息（如果需要）
	var entityInfo *models.EntityInfo
	var err error
	if req.QueryType != models.QueryTypeCompare && req.QueryType != models.QueryTypeRank {
		entityInfo, err = s.resolveEntity(ctx, &req.Entity)
		if err != nil {
			log.Printf("[executeCompositeMetricQuery] Entity resolution failed: %v", err)
			compositeData["error"] = fmt.Sprintf("Entity resolution failed: %v", err)
			return compositeData
		}
	}

	// 查询每个组件指标
	components := compositeData["components"].(map[string]interface{})
	for _, componentName := range metricConfig.Components {
		log.Printf("[executeCompositeMetricQuery] Querying component: %s", componentName)

		var componentData interface{}
		var queryErr error

		// 根据查询类型执行相应的查询
		switch req.QueryType {
		case models.QueryTypePoint:
			componentData, queryErr = s.executePointQuery(ctx, entityInfo, componentName, timeQuery, req.TimeExpression)
		case models.QueryTypeTrend:
			componentData, queryErr = s.executeTrendQuery(ctx, entityInfo, componentName, timeQuery)
		case models.QueryTypeProfile:
			componentData, queryErr = s.executePointQuery(ctx, entityInfo, componentName, timeQuery, req.TimeExpression)
		case models.QueryTypeCompare:
			componentData, queryErr = s.executeCompareQuery(ctx, &req.Entity, componentName, timeQuery, req.TimeExpression)
		case models.QueryTypeRank:
			limit := 10
			if req.Limit > 0 {
				limit = req.Limit
			}
			componentData, queryErr = s.executeRankQuery(ctx, &req.Entity, componentName, timeQuery, limit)
		case models.QueryTypeCompose:
			componentData, queryErr = s.executeComposeQuery(ctx, entityInfo, componentName, timeQuery, req.Filters)
		default:
			log.Printf("[executeCompositeMetricQuery] Unsupported query type: %s", req.QueryType)
			continue
		}

		if queryErr != nil {
			log.Printf("[executeCompositeMetricQuery] Failed to query component %s: %v", componentName, queryErr)
			// 使用降级处理
			entityName := ""
			if entityInfo != nil {
				entityName = entityInfo.Name
			}
			fallbackResult := s.UnifiedFallback(ctx, req.Entity.Type, entityName, componentName, req.TimeExpression)

			// 获取组件指标的单位
			componentUnit, _ := s.metricService.GetUnit(componentName)
			components[componentName] = map[string]interface{}{
				"value":  fallbackResult.Value,
				"unit":   componentUnit,
				"source": "fallback",
			}
		} else if componentData != nil {
			components[componentName] = componentData
		} else {
			// 数据为空，使用降级处理
			entityName := ""
			if entityInfo != nil {
				entityName = entityInfo.Name
			}
			fallbackResult := s.UnifiedFallback(ctx, req.Entity.Type, entityName, componentName, req.TimeExpression)

			// 获取组件指标的单位
			componentUnit, _ := s.metricService.GetUnit(componentName)
			components[componentName] = map[string]interface{}{
				"value":  fallbackResult.Value,
				"unit":   componentUnit,
				"source": "fallback",
			}
		}
	}

	// 添加复合指标的元信息
	compositeData["description"] = metricConfig.Description
	compositeData["category"] = metricConfig.Category
	compositeData["unit"] = metricConfig.Unit

	log.Printf("[executeCompositeMetricQuery] Composite metric query completed for: %s", metricConfig.ChineseName)
	return compositeData
}

// executeQueryForAllMetrics 执行所有指标的查询
func (s *QueryService) executeQueryForAllMetrics(ctx context.Context, req *models.UnifiedQueryRequest, timeQuery *timeparser.TimeQuery) (*models.UnifiedQueryResponse, error) {
	log.Printf("[executeQueryForAllMetrics] Executing query for all metrics of entity type: %s, query type: %s", req.Entity.Type, req.QueryType)

	// 获取该实体类型的所有可用指标
	availableMetrics := s.metricService.GetAvailableMetrics(req.Entity.Type)
	if len(availableMetrics) == 0 {
		return &models.UnifiedQueryResponse{
			Status: "error",
			Error:  fmt.Sprintf("No metrics available for entity type: %s", req.Entity.Type),
		}, nil
	}

	// 对于比较查询和排名查询，不需要解析单个实体
	var entityInfo *models.EntityInfo
	var err error
	if req.QueryType != models.QueryTypeCompare && req.QueryType != models.QueryTypeRank {
		entityInfo, err = s.resolveEntity(ctx, &req.Entity)
		if err != nil {
			log.Printf("[executeQueryForAllMetrics] Entity resolution failed: %v", err)
			return &models.UnifiedQueryResponse{
				Status: "error",
				Error:  fmt.Sprintf("Entity resolution failed: %v", err),
			}, nil
		}
	}

	// 构建所有指标的数据
	allMetricsData := make(map[string]interface{})

	for _, metricConfig := range availableMetrics {
		metricName := metricConfig.ChineseName
		log.Printf("[executeQueryForAllMetrics] Querying metric: %s with query type: %s", metricName, req.QueryType)

		// 检查是否为复合指标
		if metricConfig.IsComposite {
			log.Printf("[executeQueryForAllMetrics] Processing composite metric: %s", metricName)
			compositeData := s.executeCompositeMetricQuery(ctx, req, timeQuery, metricConfig)
			allMetricsData[metricName] = compositeData
			continue
		}

		// 为每个指标执行对应类型的查询
		var data interface{}
		var queryErr error

		switch req.QueryType {
		case models.QueryTypePoint:
			data, queryErr = s.executePointQuery(ctx, entityInfo, metricName, timeQuery, req.TimeExpression)
		case models.QueryTypeTrend:
			data, queryErr = s.executeTrendQuery(ctx, entityInfo, metricName, timeQuery)
		case models.QueryTypeProfile:
			data, queryErr = s.executePointQuery(ctx, entityInfo, metricName, timeQuery, req.TimeExpression)
		case models.QueryTypeCompare:
			data, queryErr = s.executeCompareQuery(ctx, &req.Entity, metricName, timeQuery, req.TimeExpression)
		case models.QueryTypeRank:
			limit := 10 // 默认限制
			if req.Limit > 0 {
				limit = req.Limit
			}
			data, queryErr = s.executeRankQuery(ctx, &req.Entity, metricName, timeQuery, limit)
		case models.QueryTypeCompose:
			data, queryErr = s.executeComposeQuery(ctx, entityInfo, metricName, timeQuery, req.Filters)
		default:
			log.Printf("[executeQueryForAllMetrics] Unsupported query type: %s", req.QueryType)
			continue
		}

		if queryErr != nil {
			log.Printf("[executeQueryForAllMetrics] Failed to query metric %s: %v", metricName, queryErr)
			// 使用降级处理
			entityName := ""
			if entityInfo != nil {
				entityName = entityInfo.Name
			}
			fallbackResult := s.UnifiedFallback(ctx, req.Entity.Type, entityName, metricName, req.TimeExpression)
			allMetricsData[metricName] = map[string]interface{}{
				"value":  fallbackResult.Value,
				"unit":   metricConfig.Unit,
				"source": "fallback",
			}
		} else if data != nil {
			allMetricsData[metricName] = data
		} else {
			// 数据为空，使用降级处理
			entityName := ""
			if entityInfo != nil {
				entityName = entityInfo.Name
			}
			fallbackResult := s.UnifiedFallback(ctx, req.Entity.Type, entityName, metricName, req.TimeExpression)
			allMetricsData[metricName] = map[string]interface{}{
				"value":  fallbackResult.Value,
				"unit":   metricConfig.Unit,
				"source": "fallback",
			}
		}
	}

	// 构建响应
	entityName := "多个实体"
	if entityInfo != nil {
		entityName = entityInfo.Name
	}

	response := &models.UnifiedQueryResponse{
		Entity: entityName,
		Metric: "全部指标", // 表示返回所有指标
		Period: req.TimeExpression,
		Data:   allMetricsData,
		Status: "success",
	}

	log.Printf("[executeQueryForAllMetrics] Query completed for %d metrics", len(availableMetrics))
	return response, nil
}

// executeComprehensiveCompareQuery 执行综合比较查询（复用画像查询逻辑）
func (s *QueryService) executeComprehensiveCompareQuery(ctx context.Context, entityReq *models.EntityRequest, timeQuery *timeparser.TimeQuery) (interface{}, error) {
	// 解析实体列表
	identifiers, isList := entityReq.Identifier.([]interface{})
	if !isList {
		return nil, fmt.Errorf("comprehensive compare query requires entity list")
	}

	var entityNames []string
	for _, id := range identifiers {
		if name, ok := id.(string); ok {
			entityNames = append(entityNames, name)
		}
	}

	if len(entityNames) == 0 {
		return nil, fmt.Errorf("no valid entity names found")
	}

	log.Printf("[executeComprehensiveCompareQuery] Processing %d entities: %v", len(entityNames), entityNames)

	// 为每个实体执行画像查询（现在画像查询已经正确处理时间表达式）
	var compareResults []map[string]interface{}
	for _, entityName := range entityNames {
		// 构建单个实体信息
		entityInfo := &models.EntityInfo{
			Type: entityReq.Type,
			Name: entityName,
		}

		// 复用画像查询逻辑（已修复时间处理）
		profileData, err := s.executeProfileQuery(ctx, entityInfo, timeQuery, nil)
		if err != nil {
			log.Printf("[executeComprehensiveCompareQuery] Profile query failed for entity %s: %v", entityName, err)
			// 继续处理其他实体，不因单个实体失败而中断
			compareResults = append(compareResults, map[string]interface{}{
				"entity_name": entityName,
				"error":       err.Error(),
			})
			continue
		}

		// 包装为比较格式
		entityResult := map[string]interface{}{
			"entity_name": entityName,
		}

		// 如果画像数据是map，直接合并；如果是其他格式，放在data字段
		if profileMap, ok := profileData.(map[string]interface{}); ok {
			for key, value := range profileMap {
				entityResult[key] = value
			}
		} else {
			entityResult["data"] = profileData
		}

		compareResults = append(compareResults, entityResult)
	}

	log.Printf("[executeComprehensiveCompareQuery] Completed comparison for %d entities", len(compareResults))
	return compareResults, nil
}
