package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"neoapi/internal/models"
	timeparser "neoapi/internal/time"
	"neoapi/internal/utils"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

// QueryService 统一查询服务
type QueryService struct {
	driver        neo4j.DriverWithContext
	timeParser    *timeparser.CSTELParser
	metricService *MetricService
	routeService  *RouteService
}

// NewQueryService 创建新的查询服务
func NewQueryService(driver neo4j.DriverWithContext) *QueryService {
	return &QueryService{
		driver:        driver,
		timeParser:    timeparser.NewCSTELParser(),
		metricService: NewMetricService(),
		routeService:  NewRouteService(driver),
	}
}

// ExecuteQuery 执行统一查询
func (s *QueryService) ExecuteQuery(ctx context.Context, req *models.UnifiedQueryRequest) (*models.UnifiedQueryResponse, error) {
	startTime := time.Now()

	log.Printf("[ExecuteQuery] Starting query - Type: %s, Entity: %+v, Metric: %s, TimeExpression: %s",
		req.QueryType, req.Entity, req.Metric, req.TimeExpression)

	// 解析时间表达式
	timeQuery, err := s.timeParser.Parse(req.TimeExpression)
	if err != nil {
		log.Printf("[ExecuteQuery] Time expression parsing failed - expression: '%s', error: %v", req.TimeExpression, err)
		return &models.UnifiedQueryResponse{
			Status: "error",
			Error:  fmt.Sprintf("Invalid time expression: %v", err),
		}, nil
	}

	log.Printf("[ExecuteQuery] Time expression parsed successfully - periods: %v", timeQuery.MonthNodes)

	// 对于比较查询和排名查询，不需要解析单个实体
	var entityInfo *models.EntityInfo
	if req.QueryType != models.QueryTypeCompare && req.QueryType != models.QueryTypeRank {
		// 解析实体标识符
		var err error
		entityInfo, err = s.resolveEntity(ctx, &req.Entity)
		if err != nil {
			log.Printf("[ExecuteQuery] Entity resolution failed - entity: %+v, error: %v", req.Entity, err)
			return &models.UnifiedQueryResponse{
				Status: "error",
				Error:  fmt.Sprintf("Entity resolution failed: %v", err),
			}, nil
		}
		log.Printf("[ExecuteQuery] Entity resolved successfully - entityInfo: %+v", entityInfo)
	} else {
		log.Printf("[ExecuteQuery] Skipping entity resolution for %s query", req.QueryType)
	}

	// 优先检查是否有过滤器，如果有则降级处理（优先级高于空metric处理）
	if utils.HasFilters(req.Filters) {
		log.Printf("[ExecuteQuery] Filters detected, switching to filtered dimension query")
		data, err := s.executeFilteredDimensionQuery(ctx, entityInfo, req.Filters, timeQuery)
		if err != nil {
			return &models.UnifiedQueryResponse{
				Status: "error",
				Error:  err.Error(),
			}, nil
		}

		return &models.UnifiedQueryResponse{
			Entity: func() string {
				if identifier, ok := req.Entity.Identifier.(string); ok {
					return identifier
				}
				return ""
			}(),
			Metric: req.Metric, // 保持原有的metric字段
			Period: req.TimeExpression,
			Data:   data,
			Status: "success",
		}, nil
	}

	// 处理空metric的情况 - 但COMPARE和PROFILE查询需要特殊处理
	if req.Metric == "" && req.QueryType != models.QueryTypeCompare && req.QueryType != models.QueryTypeProfile {
		log.Printf("[ExecuteQuery] Empty metric detected, returning all metrics for entity type: %s", req.Entity.Type)
		return s.executeQueryForAllMetrics(ctx, req, timeQuery)
	}

	// 检查是否为复合指标
	metricConfig, err := s.metricService.GetMetricConfig(req.Metric)
	if err == nil && metricConfig.IsComposite {
		log.Printf("[ExecuteQuery] Processing composite metric: %s", req.Metric)
		compositeData := s.executeCompositeMetricQuery(ctx, req, timeQuery, metricConfig)
		return &models.UnifiedQueryResponse{
			Metric: req.Metric,
			Data:   compositeData,
			Status: "success",
			Entity: func() string {
				if identifier, ok := req.Entity.Identifier.(string); ok {
					return identifier
				}
				return ""
			}(),
			Entities: func() []string {
				if identifiers, isList := req.Entity.Identifier.([]interface{}); isList {
					var entityNames []string
					for _, id := range identifiers {
						if name, ok := id.(string); ok {
							entityNames = append(entityNames, name)
						}
					}
					return entityNames
				}
				return nil
			}(),
			Period: req.TimeExpression,
		}, nil
	}

	// 根据查询类型执行相应的查询
	var data interface{}
	switch req.QueryType {
	case models.QueryTypePoint:
		data, err = s.executePointQuery(ctx, entityInfo, req.Metric, timeQuery, req.TimeExpression)
	case models.QueryTypeProfile:
		data, err = s.executeProfileQuery(ctx, entityInfo, timeQuery, nil)
	case models.QueryTypeTrend:
		data, err = s.executeTrendQuery(ctx, entityInfo, req.Metric, timeQuery)
	case models.QueryTypeCompare:
		// 检查是否为综合比较查询（空metric或"全部指标"且无filter）
		hasFiltersResult := utils.HasFilters(req.Filters)
		isComprehensive := (req.Metric == "" || req.Metric == "全部指标") && !hasFiltersResult
		log.Printf("[ExecuteQuery] COMPARE query analysis - metric: '%s', hasFilters: %v, isComprehensive: %v", req.Metric, hasFiltersResult, isComprehensive)

		if isComprehensive {
			log.Printf("[ExecuteQuery] Comprehensive comparison triggered - metric: '%s', filters: %v", req.Metric, req.Filters)
			data, err = s.executeComprehensiveCompareQuery(ctx, &req.Entity, timeQuery)
		} else {
			log.Printf("[ExecuteQuery] Executing precise compare query with metric: %s", req.Metric)
			data, err = s.executeCompareQuery(ctx, &req.Entity, req.Metric, timeQuery, req.TimeExpression)
		}
	case models.QueryTypeRank:
		data, err = s.executeRankQuery(ctx, &req.Entity, req.Metric, timeQuery, req.Limit)
	case models.QueryTypeCompose:
		data, err = s.executeComposeQuery(ctx, entityInfo, req.Metric, timeQuery, nil)
	default:
		return &models.UnifiedQueryResponse{
			Status: "error",
			Error:  fmt.Sprintf("Unsupported query type: %s", req.QueryType),
		}, nil
	}

	if err != nil {
		response := &models.UnifiedQueryResponse{
			Metric: req.Metric,
			Period: req.TimeExpression,
			Status: "error",
			Error:  err.Error(),
		}

		// 设置实体信息（错误情况下）
		if identifiers, isList := req.Entity.Identifier.([]interface{}); isList {
			var entityNames []string
			for _, id := range identifiers {
				if name, ok := id.(string); ok {
					entityNames = append(entityNames, name)
				}
			}
			response.Entities = entityNames
		} else if identifier, ok := req.Entity.Identifier.(string); ok {
			response.Entity = identifier
		}

		// 打印错误响应的JSON
		if jsonBytes, err := json.MarshalIndent(response, "", "  "); err == nil {
			log.Printf("[ExecuteQuery] Error Response JSON:\n%s", string(jsonBytes))
		}

		return response, nil
	}

	// 构建新的统一响应格式
	response := &models.UnifiedQueryResponse{
		Metric: req.Metric,
		Data:   data,
		Status: "success",
	}

	// 设置实体信息
	if identifiers, isList := req.Entity.Identifier.([]interface{}); isList {
		// 多个实体（COMPARE查询）
		var entityNames []string
		for _, id := range identifiers {
			if name, ok := id.(string); ok {
				entityNames = append(entityNames, name)
			}
		}
		response.Entities = entityNames
	} else if identifier, ok := req.Entity.Identifier.(string); ok {
		// 单个实体
		response.Entity = identifier
	}

	// 设置时间段
	if len(timeQuery.MonthNodes) > 0 {
		if len(timeQuery.MonthNodes) == 1 {
			response.Period = timeQuery.MonthNodes[0]
		} else {
			response.Period = req.TimeExpression // 使用原始表达式
		}
	} else {
		response.Period = req.TimeExpression
	}

	log.Printf("[ExecuteQuery] Query completed successfully - Type: %s, Duration: %v", req.QueryType, time.Since(startTime))

	// 打印格式化的JSON响应结果
	// if jsonBytes, err := json.MarshalIndent(response, "", "  "); err == nil {
	// 	log.Printf("[ExecuteQuery] Response JSON:\n%s", string(jsonBytes))
	// } else {
	// 	log.Printf("[ExecuteQuery] Failed to marshal response to JSON: %v", err)
	// }

	return response, nil
}

// resolveEntity 解析实体标识符
func (s *QueryService) resolveEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	switch entityReq.Type {
	case models.EntityTypeShip:
		return s.resolveShipEntity(ctx, entityReq)
	case models.EntityTypePort:
		return s.resolvePortEntity(ctx, entityReq)
	case models.EntityTypeProvince:
		return s.resolveProvinceEntity(ctx, entityReq)
	case models.EntityTypeBasin:
		return s.resolveBasinEntity(ctx, entityReq)
	case models.EntityTypeShippingRoute:
		return s.resolveShippingRouteEntity(ctx, entityReq)
	default:
		return nil, fmt.Errorf("unsupported entity type: %s", entityReq.Type)
	}
}

// resolveShipEntity 解析船舶实体
func (s *QueryService) resolveShipEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	// 支持字符串和字符串数组
	var identifier string
	switch v := entityReq.Identifier.(type) {
	case string:
		identifier = v
	case []interface{}:
		if len(v) > 0 {
			if str, ok := v[0].(string); ok {
				identifier = str
			} else {
				return nil, fmt.Errorf("ship identifier array must contain strings")
			}
		} else {
			return nil, fmt.Errorf("ship identifier array cannot be empty")
		}
	case []string:
		if len(v) > 0 {
			identifier = v[0]
		} else {
			return nil, fmt.Errorf("ship identifier array cannot be empty")
		}
	default:
		return nil, fmt.Errorf("ship identifier must be a string or string array, got %T", entityReq.Identifier)
	}

	log.Printf("[resolveShipEntity] Resolving ship identifier: '%s'", identifier)

	var query string
	var params map[string]interface{}

	if entityReq.ResolutionStrategy == models.ResolutionExact {
		// 精确匹配MMSI
		query = `MATCH (s:Ship {mmsi: $identifier}) RETURN s.mmsi as mmsi, s.name as name, s.shipId as shipId`
		params = map[string]interface{}{"identifier": identifier}
	} else {
		// 模糊匹配船名或MMSI
		query = `
			MATCH (s:Ship)
			WHERE toLower(s.name) CONTAINS toLower($identifier) OR s.mmsi CONTAINS $identifier
			RETURN s.mmsi as mmsi, s.name as name, s.shipId as shipId
			LIMIT 1
		`
		params = map[string]interface{}{"identifier": identifier}
	}

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, fmt.Errorf("ship not found: %s", identifier)
		}

		record := records.Record()
		return &models.EntityInfo{
			Type:             models.EntityTypeShip,
			Name:             record.Values[1].(string),
			MMSI:             record.Values[0].(string),
			ShipID:           record.Values[2].(string),
			ResolutionMethod: string(entityReq.ResolutionStrategy),
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.EntityInfo), nil
}

// resolvePortEntity 解析港口实体
func (s *QueryService) resolvePortEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	// 支持字符串和字符串数组
	var identifier string
	switch v := entityReq.Identifier.(type) {
	case string:
		identifier = v
	case []interface{}:
		if len(v) > 0 {
			if str, ok := v[0].(string); ok {
				identifier = str
			} else {
				return nil, fmt.Errorf("port identifier array must contain strings")
			}
		} else {
			return nil, fmt.Errorf("port identifier array cannot be empty")
		}
	case []string:
		if len(v) > 0 {
			identifier = v[0]
		} else {
			return nil, fmt.Errorf("port identifier array cannot be empty")
		}
	default:
		return nil, fmt.Errorf("port identifier must be a string or string array, got %T", entityReq.Identifier)
	}

	log.Printf("[resolvePortEntity] Resolving port identifier: '%s'", identifier)

	// 去除常见的港口后缀
	cleanedName := s.removeEntitySuffixes(identifier, []string{"港", "港口", "码头"})
	log.Printf("[resolvePortEntity] Cleaned port name: '%s' -> '%s'", identifier, cleanedName)

	return &models.EntityInfo{
		Type:             models.EntityTypePort,
		Name:             cleanedName,
		ResolutionMethod: string(entityReq.ResolutionStrategy),
	}, nil
}

// resolveProvinceEntity 解析省份实体
func (s *QueryService) resolveProvinceEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	identifier, ok := entityReq.Identifier.(string)
	if !ok {
		return nil, fmt.Errorf("province identifier must be a string")
	}

	return &models.EntityInfo{
		Type:             models.EntityTypeProvince,
		Name:             identifier,
		ResolutionMethod: string(entityReq.ResolutionStrategy),
	}, nil
}

// resolveBasinEntity 解析流域实体
func (s *QueryService) resolveBasinEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	identifier, ok := entityReq.Identifier.(string)
	if !ok {
		return nil, fmt.Errorf("basin identifier must be a string")
	}

	return &models.EntityInfo{
		Type:             models.EntityTypeBasin,
		Name:             identifier,
		ResolutionMethod: string(entityReq.ResolutionStrategy),
	}, nil
}

// resolveShippingRouteEntity 解析航线实体
func (s *QueryService) resolveShippingRouteEntity(ctx context.Context, entityReq *models.EntityRequest) (*models.EntityInfo, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	identifier, ok := entityReq.Identifier.(string)
	if !ok {
		return nil, fmt.Errorf("shipping route identifier must be a string")
	}

	query := `
		MATCH (sr:ShippingRoute)
		WHERE toLower(sr.routeName) CONTAINS toLower($identifier) OR sr.routeId = $identifier
		OPTIONAL MATCH (sr)-[:ROUTE_ORIGIN]->(pO:Port)
		OPTIONAL MATCH (sr)-[:ROUTE_DESTINATION]->(pD:Port)
		RETURN sr.routeId as routeId, sr.routeName as routeName, 
		       pO.name as originPort, pD.name as destinationPort, sr.distance_km as distance
		LIMIT 1
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{"identifier": identifier}
		log.Printf("[resolveShippingRouteEntity] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, fmt.Errorf("shipping route not found: %s", identifier)
		}

		record := records.Record()
		return &models.EntityInfo{
			Type:             models.EntityTypeShippingRoute,
			RouteID:          record.Values[0].(string),
			RouteName:        record.Values[1].(string),
			OriginPort:       utils.GetStringValue(record.Values[2]),
			DestinationPort:  utils.GetStringValue(record.Values[3]),
			DistanceKm:       utils.GetFloatValue(record.Values[4]),
			ResolutionMethod: string(entityReq.ResolutionStrategy),
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.EntityInfo), nil
}

// mapMetricToDBField 将指标名称映射到数据库字段（使用MetricService）
func (s *QueryService) mapMetricToDBField(metric string) string {
	dbField, err := s.metricService.GetDBField(metric)
	if err != nil {
		log.Printf("[QueryService] Failed to map metric '%s' to DB field: %v", metric, err)
		return metric // 如果没有映射，返回原始值
	}
	return dbField
}

// getMetricUnit 获取指标单位（使用MetricService）
func (s *QueryService) getMetricUnit(metric string) string {
	unit, err := s.metricService.GetUnit(metric)
	if err != nil {
		log.Printf("[QueryService] Failed to get unit for metric '%s': %v", metric, err)
		return ""
	}
	return unit
}

// GetShipRealtimeData 获取船舶实时数据
func (s *QueryService) GetShipRealtimeData(ctx context.Context, identifier string) (*models.RealtimeData, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{AccessMode: neo4j.AccessModeRead})
	defer session.Close(ctx)

	// 查询船舶实时数据
	query := `
		MATCH (s:Ship)-[:HAS_REALTIME_STATUS]->(sr:ShipRealtime)
		WHERE s.name = $identifier OR s.mmsi = $identifier
		RETURN s.name as ship_name, s.mmsi as mmsi,
		       sr.lat as lat, sr.lon as lon, sr.sog as sog, sr.cog as cog,
		       sr.navStatus as nav_status, sr.aisTimestamp as ais_timestamp,
		       sr.km as km, sr.portAis as port_ais, sr.orgName as org_name,
		       sr.portStatus as port_status, sr.portReport as port_report,
		       sr.reportTimeIn as report_time_in, sr.reportTimeOut as report_time_out,
		       sr.actualCarryCapacityIn_ton as actual_carry_capacity_in,
		       sr.actualCarryCapacityOut_ton as actual_carry_capacity_out,
		       sr.portCargoIn_to as port_cargo_in, sr.portCargoOut_ton as port_cargo_out,
		       sr.lastUpdated as last_updated
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{
			"identifier": identifier,
		})
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, fmt.Errorf("ship not found")
		}

		record := records.Record()

		// 构建实时数据响应
		realtimeData := &models.RealtimeData{
			Entity: models.EntityInfo{
				Type: models.EntityTypeShip,
				Name: utils.GetStringValue(record.Values[0]),
				MMSI: utils.GetStringValue(record.Values[1]),
			},
			RealtimeData: map[string]interface{}{
				"position": map[string]interface{}{
					"lat":       utils.GetFloatValue(record.Values[2]),
					"lon":       utils.GetFloatValue(record.Values[3]),
					"timestamp": utils.GetStringValue(record.Values[7]),
				},
				"navigation": map[string]interface{}{
					"sog":       utils.GetFloatValue(record.Values[4]),
					"cog":       utils.GetFloatValue(record.Values[5]),
					"navStatus": utils.GetStringValue(record.Values[6]),
				},
				"location": map[string]interface{}{
					"km":      utils.GetFloatValue(record.Values[8]),
					"portAis": utils.GetStringValue(record.Values[9]),
					"orgName": utils.GetStringValue(record.Values[10]),
				},
				"port_status": map[string]interface{}{
					"status":        utils.GetStringValue(record.Values[11]),
					"report":        utils.GetStringValue(record.Values[12]),
					"reportTimeIn":  utils.GetStringValue(record.Values[13]),
					"reportTimeOut": utils.GetStringValue(record.Values[14]),
				},
				"cargo_status": map[string]interface{}{
					"carryCapacityIn":  utils.GetFloatValue(record.Values[15]),
					"carryCapacityOut": utils.GetFloatValue(record.Values[16]),
					"cargoIn":          utils.GetStringValue(record.Values[17]),
					"cargoOut":         utils.GetStringValue(record.Values[18]),
				},
			},
			Status: "success",
		}

		return realtimeData, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.RealtimeData), nil
}

// // GetEntityFullProfile 获取实体的完整属性
// func (s *QueryService) GetEntityFullProfile(ctx context.Context, entityType models.EntityType, identifier string, timeExpression string) (*models.EntityProfileData, error) {
// 	switch entityType {
// 	case models.EntityTypeShip:
// 		return s.getShipFullProfile(ctx, identifier, timeExpression)
// 	case models.EntityTypePort:
// 		return s.getPortFullProfile(ctx, identifier, timeExpression)
// 	default:
// 		return nil, fmt.Errorf("unsupported entity type for full profile: %s", entityType)
// 	}
// }

// // getShipFullProfile 获取船舶完整属性
// func (s *QueryService) getShipFullProfile(ctx context.Context, shipName string, timeExpression string) (*models.EntityProfileData, error) {
// 	// 基本信息
// 	basicInfo := map[string]interface{}{
// 		"name": shipName,
// 		"type": "Ship",
// 	}

// 	// 尝试获取所有可用指标
// 	availableMetrics := make(map[string]interface{})

// 	// 定义船舶的所有可能指标
// 	shipMetrics := []string{
// 		"有效营运率", "载货量", "航次数", "载重率", "周转量",
// 		"货运量", "平均载重", "营运天数", "停泊天数",
// 	}

// 	for _, metric := range shipMetrics {
// 		value, unit, err := s.getMetricValue(ctx, models.EntityTypeShip, shipName, metric, timeExpression)
// 		if err == nil && value != nil {
// 			metricData := map[string]interface{}{
// 				"value": value,
// 				"unit":  unit,
// 			}
// 			availableMetrics[metric] = metricData
// 		}
// 	}

// 	// 如果没有任何指标可用，尝试获取基本统计信息
// 	if len(availableMetrics) == 0 {
// 		// 添加一些基本的存在性信息
// 		availableMetrics["entity_exists"] = map[string]interface{}{
// 			"value": true,
// 			"unit":  "boolean",
// 		}
// 		availableMetrics["last_activity"] = map[string]interface{}{
// 			"value": "数据查询中",
// 			"unit":  "status",
// 		}
// 	}

// 	return &models.EntityProfileData{
// 		BasicInfo:        basicInfo,
// 		AvailableMetrics: availableMetrics,
// 		TimeRange:        timeExpression,
// 		LastUpdated:      time.Now().Format("2006-01-02T15:04:05Z"),
// 		DataSources:      []string{"neo4j", "mongodb"},
// 	}, nil
// }

// // getPortFullProfile 获取港口完整属性
// func (s *QueryService) getPortFullProfile(ctx context.Context, portName string, timeExpression string) (*models.EntityProfileData, error) {
// 	// 基本信息
// 	basicInfo := map[string]interface{}{
// 		"name": portName,
// 		"type": "Port",
// 	}

// 	// 尝试获取所有可用指标
// 	availableMetrics := make(map[string]interface{})

// 	// 定义港口的所有可能指标
// 	portMetrics := []string{
// 		"总吞吐量", "进港艘次", "出港艘次", "进港货量", "出港货量",
// 		"集装箱吞吐量", "散货吞吐量", "液体货吞吐量", "平均停泊时间",
// 	}

// 	for _, metric := range portMetrics {
// 		value, unit, err := s.getMetricValue(ctx, models.EntityTypePort, portName, metric, timeExpression)
// 		if err == nil && value != nil {
// 			metricData := map[string]interface{}{
// 				"value": value,
// 				"unit":  unit,
// 			}
// 			availableMetrics[metric] = metricData
// 		}
// 	}

// 	// 如果没有任何指标可用，尝试获取基本统计信息
// 	if len(availableMetrics) == 0 {
// 		// 添加一些基本的存在性信息
// 		availableMetrics["entity_exists"] = map[string]interface{}{
// 			"value": true,
// 			"unit":  "boolean",
// 		}
// 		availableMetrics["last_activity"] = map[string]interface{}{
// 			"value": "数据查询中",
// 			"unit":  "status",
// 		}
// 	}

// 	return &models.EntityProfileData{
// 		BasicInfo:        basicInfo,
// 		AvailableMetrics: availableMetrics,
// 		TimeRange:        timeExpression,
// 		LastUpdated:      time.Now().Format("2006-01-02T15:04:05Z"),
// 		DataSources:      []string{"neo4j", "mongodb"},
// 	}, nil
// }

// getMetricValue 获取单个指标值
func (s *QueryService) getMetricValue(ctx context.Context, entityType models.EntityType, identifier string, metric string, timeExpression string) (interface{}, string, error) {
	// 解析时间表达式
	timeQuery, err := s.timeParser.Parse(timeExpression)
	if err != nil {
		return nil, "", err
	}

	if len(timeQuery.MonthNodes) == 0 {
		return nil, "", fmt.Errorf("no time periods specified")
	}

	period := timeQuery.MonthNodes[0]
	dbField := s.mapMetricToDBField(metric)
	unit := s.getMetricUnit(metric)

	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	var params map[string]interface{}

	switch entityType {
	case models.EntityTypeShip:
		query = fmt.Sprintf(`
			MATCH (s:Ship {name: $entityName})<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			RETURN sms.%s as value
		`, dbField)
	case models.EntityTypePort:
		query = fmt.Sprintf(`
			MATCH (p:Port {name: $entityName})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			RETURN pms.%s as value
		`, dbField)
	default:
		return nil, "", fmt.Errorf("unsupported entity type: %s", entityType)
	}

	params = map[string]interface{}{
		"entityName": identifier,
		"period":     period,
	}

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, nil // 没有数据
		}

		return records.Record().Values[0], nil
	})

	if err != nil {
		return nil, unit, err
	}

	return result, unit, nil
}

// UnifiedFallback 统一降级处理 - 简单版本
func (s *QueryService) UnifiedFallback(ctx context.Context, entityType models.EntityType, entityName string, metric string, timeExpression string) *models.FallbackResult {
	log.Printf("[UnifiedFallback] Processing fallback for entity: %s, metric: %s", entityName, metric)

	// 步骤1: 尝试时间降级（去年同期）
	if fallbackData := s.tryTimeFallback(ctx, entityType, entityName, metric, timeExpression); fallbackData != nil {
		return fallbackData
	}

	// 步骤2: 检查实体是否存在并返回基本信息
	return s.tryEntityExistsFallback(ctx, entityType, entityName, metric)
}

// tryTimeFallback 尝试时间降级（去年同期）
func (s *QueryService) tryTimeFallback(ctx context.Context, entityType models.EntityType, entityName string, metric string, timeExpression string) *models.FallbackResult {
	lastYearExpr := s.convertToLastYear(timeExpression)
	if lastYearExpr == "" {
		return nil // 无法转换时间，跳过时间降级
	}

	value, unit, err := s.getMetricValue(ctx, entityType, entityName, metric, lastYearExpr)
	if err == nil && value != nil {
		return &models.FallbackResult{
			Value:        value,
			Unit:         unit,
			Status:       "time_fallback_last_year",
			Message:      "当前时间段无数据，已返回去年同期数据",
			FallbackType: "time_fallback",
			AlternativeInfo: map[string]interface{}{
				"original_time": timeExpression,
				"fallback_time": lastYearExpr,
			},
		}
	}
	return nil
}

// tryEntityExistsFallback 检查实体存在性并返回基本信息
func (s *QueryService) tryEntityExistsFallback(ctx context.Context, entityType models.EntityType, entityName string, metric string) *models.FallbackResult {
	exists, basicInfo := s.checkEntityExistsWithBasicInfo(ctx, entityType, entityName)

	if exists {
		return &models.FallbackResult{
			Value:           nil,
			Status:          "metric_unavailable_entity_exists",
			Message:         fmt.Sprintf("指标'%s'不可用，但实体存在", metric),
			FallbackType:    "entity_exists",
			AlternativeInfo: basicInfo,
		}
	}

	return &models.FallbackResult{
		Value:        nil,
		Status:       "entity_not_found",
		Message:      fmt.Sprintf("实体'%s'不存在", entityName),
		FallbackType: "not_found",
	}
}

// convertToLastYear 简单的去年时间转换
func (s *QueryService) convertToLastYear(timeExpression string) string {
	// R6M 等相对时间保持不变（系统会自动计算去年同期）
	if strings.HasPrefix(timeExpression, "R") {
		return timeExpression
	}

	// M202508 -> M202408 (月份格式转换)
	if strings.HasPrefix(timeExpression, "M") && len(timeExpression) == 7 {
		year := timeExpression[1:5]
		month := timeExpression[5:7]
		if yearInt, err := strconv.Atoi(year); err == nil {
			return fmt.Sprintf("M%04d%s", yearInt-1, month)
		}
	}

	return "" // 无法转换的格式
}

// checkEntityExistsWithBasicInfo 检查实体存在性并获取基本信息
func (s *QueryService) checkEntityExistsWithBasicInfo(ctx context.Context, entityType models.EntityType, entityName string) (bool, map[string]interface{}) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	switch entityType {
	case models.EntityTypeShip:
		query = `
			MATCH (s:Ship {name: $entityName})
			RETURN s.name as name, s.mmsi as mmsi, s.owner as owner
		`
	case models.EntityTypePort:
		query = `
			MATCH (p:Port {name: $entityName})
			OPTIONAL MATCH (p)-[:BELONGS_TO_PROVINCE]->(prov:Province)
			RETURN p.name as name, p.code as code, prov.name as province
		`
	default:
		return false, nil
	}

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{"entityName": entityName}
		log.Printf("[checkEntityExistsWithBasicInfo] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, nil // 实体不存在
		}

		record := records.Record()
		basicInfo := map[string]interface{}{
			"entity_type": string(entityType),
		}

		// 添加查询到的基本信息
		for i, key := range record.Keys {
			if record.Values[i] != nil {
				basicInfo[key] = getStringValue(record.Values[i])
			}
		}

		return basicInfo, nil
	})

	if err != nil || result == nil {
		return false, nil
	}

	return true, result.(map[string]interface{})
}

// // getDefaultMetricForEntityType 根据实体类型获取默认指标
// func (s *QueryService) getDefaultMetricForEntityType(entityType models.EntityType) string {
// 	// 使用MetricService获取该实体类型的第一个可用指标作为默认指标
// 	availableMetrics := s.metricService.GetAvailableMetrics(entityType)
// 	if len(availableMetrics) > 0 {
// 		// 优先选择常用的指标
// 		for _, metric := range availableMetrics {
// 			switch entityType {
// 			case models.EntityTypeShip:
// 				if metric.ChineseName == "有效营运率" {
// 					return metric.ChineseName
// 				}
// 			case models.EntityTypePort:
// 				if metric.ChineseName == "总吞吐量" {
// 					return metric.ChineseName
// 				}
// 			case models.EntityTypeShippingRoute:
// 				if metric.ChineseName == "航线货运量" {
// 					return metric.ChineseName
// 				}
// 			}
// 		}
// 		// 如果没有找到优先指标，返回第一个可用指标
// 		return availableMetrics[0].ChineseName
// 	}

// 	log.Printf("[getDefaultMetricForEntityType] No metrics available for entity type: %s", entityType)
// 	return ""
// }

// executeCompositeMetricQuery 执行复合指标查询
func (s *QueryService) executeCompositeMetricQuery(ctx context.Context, req *models.UnifiedQueryRequest, timeQuery *timeparser.TimeQuery, metricConfig *MetricConfig) map[string]interface{} {
	log.Printf("[executeCompositeMetricQuery] Executing composite metric query for: %s", metricConfig.ChineseName)

	compositeData := make(map[string]interface{})
	compositeData["type"] = "composite"
	compositeData["components"] = make(map[string]interface{})

	// 解析实体信息（如果需要）
	var entityInfo *models.EntityInfo
	var err error
	if req.QueryType != models.QueryTypeCompare && req.QueryType != models.QueryTypeRank {
		entityInfo, err = s.resolveEntity(ctx, &req.Entity)
		if err != nil {
			log.Printf("[executeCompositeMetricQuery] Entity resolution failed: %v", err)
			compositeData["error"] = fmt.Sprintf("Entity resolution failed: %v", err)
			return compositeData
		}
	}

	// 查询每个组件指标
	components := compositeData["components"].(map[string]interface{})
	for _, componentName := range metricConfig.Components {
		log.Printf("[executeCompositeMetricQuery] Querying component: %s", componentName)

		var componentData interface{}
		var queryErr error

		// 根据查询类型执行相应的查询
		switch req.QueryType {
		case models.QueryTypePoint:
			componentData, queryErr = s.executePointQuery(ctx, entityInfo, componentName, timeQuery, req.TimeExpression)
		case models.QueryTypeTrend:
			componentData, queryErr = s.executeTrendQuery(ctx, entityInfo, componentName, timeQuery)
		case models.QueryTypeProfile:
			componentData, queryErr = s.executePointQuery(ctx, entityInfo, componentName, timeQuery, req.TimeExpression)
		case models.QueryTypeCompare:
			componentData, queryErr = s.executeCompareQuery(ctx, &req.Entity, componentName, timeQuery, req.TimeExpression)
		case models.QueryTypeRank:
			limit := 10
			if req.Limit > 0 {
				limit = req.Limit
			}
			componentData, queryErr = s.executeRankQuery(ctx, &req.Entity, componentName, timeQuery, limit)
		case models.QueryTypeCompose:
			componentData, queryErr = s.executeComposeQuery(ctx, entityInfo, componentName, timeQuery, req.Filters)
		default:
			log.Printf("[executeCompositeMetricQuery] Unsupported query type: %s", req.QueryType)
			continue
		}

		if queryErr != nil {
			log.Printf("[executeCompositeMetricQuery] Failed to query component %s: %v", componentName, queryErr)
			// 使用降级处理
			entityName := ""
			if entityInfo != nil {
				entityName = entityInfo.Name
			}
			fallbackResult := s.UnifiedFallback(ctx, req.Entity.Type, entityName, componentName, req.TimeExpression)

			// 获取组件指标的单位
			componentUnit, _ := s.metricService.GetUnit(componentName)
			components[componentName] = map[string]interface{}{
				"value":  fallbackResult.Value,
				"unit":   componentUnit,
				"source": "fallback",
			}
		} else if componentData != nil {
			components[componentName] = componentData
		} else {
			// 数据为空，使用降级处理
			entityName := ""
			if entityInfo != nil {
				entityName = entityInfo.Name
			}
			fallbackResult := s.UnifiedFallback(ctx, req.Entity.Type, entityName, componentName, req.TimeExpression)

			// 获取组件指标的单位
			componentUnit, _ := s.metricService.GetUnit(componentName)
			components[componentName] = map[string]interface{}{
				"value":  fallbackResult.Value,
				"unit":   componentUnit,
				"source": "fallback",
			}
		}
	}

	// 添加复合指标的元信息
	compositeData["description"] = metricConfig.Description
	compositeData["category"] = metricConfig.Category
	compositeData["unit"] = metricConfig.Unit

	log.Printf("[executeCompositeMetricQuery] Composite metric query completed for: %s", metricConfig.ChineseName)
	return compositeData
}

// executeQueryForAllMetrics 执行所有指标的查询
func (s *QueryService) executeQueryForAllMetrics(ctx context.Context, req *models.UnifiedQueryRequest, timeQuery *timeparser.TimeQuery) (*models.UnifiedQueryResponse, error) {
	log.Printf("[executeQueryForAllMetrics] Executing query for all metrics of entity type: %s, query type: %s", req.Entity.Type, req.QueryType)

	// 获取该实体类型的所有可用指标
	availableMetrics := s.metricService.GetAvailableMetrics(req.Entity.Type)
	if len(availableMetrics) == 0 {
		return &models.UnifiedQueryResponse{
			Status: "error",
			Error:  fmt.Sprintf("No metrics available for entity type: %s", req.Entity.Type),
		}, nil
	}

	// 对于比较查询和排名查询，不需要解析单个实体
	var entityInfo *models.EntityInfo
	var err error
	if req.QueryType != models.QueryTypeCompare && req.QueryType != models.QueryTypeRank {
		entityInfo, err = s.resolveEntity(ctx, &req.Entity)
		if err != nil {
			log.Printf("[executeQueryForAllMetrics] Entity resolution failed: %v", err)
			return &models.UnifiedQueryResponse{
				Status: "error",
				Error:  fmt.Sprintf("Entity resolution failed: %v", err),
			}, nil
		}
	}

	// 构建所有指标的数据
	allMetricsData := make(map[string]interface{})

	for _, metricConfig := range availableMetrics {
		metricName := metricConfig.ChineseName
		log.Printf("[executeQueryForAllMetrics] Querying metric: %s with query type: %s", metricName, req.QueryType)

		// 检查是否为复合指标
		if metricConfig.IsComposite {
			log.Printf("[executeQueryForAllMetrics] Processing composite metric: %s", metricName)
			compositeData := s.executeCompositeMetricQuery(ctx, req, timeQuery, metricConfig)
			allMetricsData[metricName] = compositeData
			continue
		}

		// 为每个指标执行对应类型的查询
		var data interface{}
		var queryErr error

		switch req.QueryType {
		case models.QueryTypePoint:
			data, queryErr = s.executePointQuery(ctx, entityInfo, metricName, timeQuery, req.TimeExpression)
		case models.QueryTypeTrend:
			data, queryErr = s.executeTrendQuery(ctx, entityInfo, metricName, timeQuery)
		case models.QueryTypeProfile:
			data, queryErr = s.executePointQuery(ctx, entityInfo, metricName, timeQuery, req.TimeExpression)
		case models.QueryTypeCompare:
			data, queryErr = s.executeCompareQuery(ctx, &req.Entity, metricName, timeQuery, req.TimeExpression)
		case models.QueryTypeRank:
			limit := 10 // 默认限制
			if req.Limit > 0 {
				limit = req.Limit
			}
			data, queryErr = s.executeRankQuery(ctx, &req.Entity, metricName, timeQuery, limit)
		case models.QueryTypeCompose:
			data, queryErr = s.executeComposeQuery(ctx, entityInfo, metricName, timeQuery, req.Filters)
		default:
			log.Printf("[executeQueryForAllMetrics] Unsupported query type: %s", req.QueryType)
			continue
		}

		if queryErr != nil {
			log.Printf("[executeQueryForAllMetrics] Failed to query metric %s: %v", metricName, queryErr)
			// 使用降级处理
			entityName := ""
			if entityInfo != nil {
				entityName = entityInfo.Name
			}
			fallbackResult := s.UnifiedFallback(ctx, req.Entity.Type, entityName, metricName, req.TimeExpression)
			allMetricsData[metricName] = map[string]interface{}{
				"value":  fallbackResult.Value,
				"unit":   metricConfig.Unit,
				"source": "fallback",
			}
		} else if data != nil {
			allMetricsData[metricName] = data
		} else {
			// 数据为空，使用降级处理
			entityName := ""
			if entityInfo != nil {
				entityName = entityInfo.Name
			}
			fallbackResult := s.UnifiedFallback(ctx, req.Entity.Type, entityName, metricName, req.TimeExpression)
			allMetricsData[metricName] = map[string]interface{}{
				"value":  fallbackResult.Value,
				"unit":   metricConfig.Unit,
				"source": "fallback",
			}
		}
	}

	// 构建响应
	entityName := "多个实体"
	if entityInfo != nil {
		entityName = entityInfo.Name
	}

	response := &models.UnifiedQueryResponse{
		Entity: entityName,
		Metric: "全部指标", // 表示返回所有指标
		Period: req.TimeExpression,
		Data:   allMetricsData,
		Status: "success",
	}

	log.Printf("[executeQueryForAllMetrics] Query completed for %d metrics", len(availableMetrics))
	return response, nil
}

// queryCargoTypeDimension 查询货物类型维度的所有指标
func (s *QueryService) queryCargoTypeDimension(ctx context.Context, entity *models.EntityInfo, cargoType string, timeQuery *timeparser.TimeQuery) (interface{}, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	var params map[string]interface{}

	// 处理多个时间节点
	periods := timeQuery.MonthNodes

	switch entity.Type {
	case models.EntityTypeShip:
		query = `
			MATCH (s:Ship {name: $entityName})<-[:CARGO_STAT_FOR_SHIP]-(smcs:ShipMonthCargoStat)-[:CARGO_STAT_FOR_MONTH]->(ym:YearMonth)
			WHERE ym.ym IN $periods
			MATCH (smcs)-[:CARGO_STAT_FOR_TYPE]->(ct:CargoType {subName: $cargoType})
			RETURN ym.ym as period, {
				cargo_ton: smcs.cargo_ton,
				voyage_count: smcs.voyage_count,
				avg_load_ratio: smcs.avg_load_ratio,
				total_distance: smcs.total_distance
			} as metrics
			ORDER BY ym.ym
		`
		params = map[string]interface{}{
			"entityName": entity.Name,
			"cargoType":  cargoType,
			"periods":    periods,
		}
	case models.EntityTypePort:
		query = `
			MATCH (p:Port {name: $entityName})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)
			WHERE ym.ym IN $periods
			MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType {subName: $cargoType})
			RETURN ym.ym as period, {
				inCargo_ton: pmcs.inCargo_ton,
				outCargo_ton: pmcs.outCargo_ton,
				totalCargo_ton: pmcs.inCargo_ton + pmcs.outCargo_ton,
				ship_count: pmcs.ship_count
			} as metrics
			ORDER BY ym.ym
		`
		params = map[string]interface{}{
			"entityName": entity.Name,
			"cargoType":  cargoType,
			"periods":    periods,
		}
	case models.EntityTypeShippingRoute:
		query = `
			MATCH (sr:ShippingRoute {routeName: $entityName})<-[:ROUTE_CARGO_STAT_FOR_ROUTE]-(rmcs:RouteMonthCargoStat)-[:ROUTE_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)
			WHERE ym.ym IN $periods
			MATCH (rmcs)-[:ROUTE_CARGO_STAT_FOR_TYPE]->(ct:CargoType {subName: $cargoType})
			RETURN ym.ym as period, {
				cargo_ton: rmcs.cargo_ton,
				ship_count: rmcs.ship_count,
				voyage_count: rmcs.voyage_count,
				avg_load_ratio: rmcs.avg_load_ratio
			} as metrics
			ORDER BY ym.ym
		`
		params = map[string]interface{}{
			"entityName": entity.RouteName,
			"cargoType":  cargoType,
			"periods":    periods,
		}
	default:
		return nil, fmt.Errorf("unsupported entity type for cargo type dimension query: %s", entity.Type)
	}

	// 打印Cypher查询日志
	log.Printf("[queryCargoTypeDimension] Executing Cypher query:\n%s\nParams: %+v", query, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[queryCargoTypeDimension] Cypher query failed: %v", err)
			return nil, err
		}

		// 收集所有时间节点的数据
		var dataPoints []map[string]interface{}
		for records.Next(ctx) {
			record := records.Record()
			period := record.Values[0].(string)
			metrics := record.Values[1]

			dataPoints = append(dataPoints, map[string]interface{}{
				"period":  period,
				"metrics": metrics,
			})
		}

		if len(dataPoints) == 0 {
			log.Printf("[queryCargoTypeDimension] No data found for entity: %s, cargo type: %s, periods: %v", entity.Name, cargoType, periods)
			return []map[string]interface{}{}, nil
		}

		log.Printf("[queryCargoTypeDimension] Found %d data points for entity: %s, cargo type: %s", len(dataPoints), entity.Name, cargoType)
		return dataPoints, nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// queryShipTypeDimension 查询船舶类型维度的所有指标
func (s *QueryService) queryShipTypeDimension(ctx context.Context, entity *models.EntityInfo, shipType string, timeQuery *timeparser.TimeQuery) (interface{}, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	var params map[string]interface{}

	// 暂时使用第一个时间节点，后续可以扩展为多时间节点
	period := timeQuery.MonthNodes[0]

	switch entity.Type {
	case models.EntityTypePort:
		// 港口按船舶类型过滤的统计
		query = `
			MATCH (p:Port {name: $entityName})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			MATCH (pms)-[:RELATED_SHIP_TYPE]->(st:ShipType {typeName: $shipType})
			RETURN {
				inShipCount: pms.inShipCount,
				outShipCount: pms.outShipCount,
				inCargo_ton: pms.inCargo_ton,
				outCargo_ton: pms.outCargo_ton,
				totalShipCount: pms.inShipCount + pms.outShipCount,
				totalCargo_ton: pms.inCargo_ton + pms.outCargo_ton
			} as metrics
		`
		params = map[string]interface{}{
			"entityName": entity.Name,
			"shipType":   shipType,
			"period":     period,
		}
	case models.EntityTypeShippingRoute:
		// 航线按船舶类型过滤的统计
		query = `
			MATCH (sr:ShippingRoute {routeName: $entityName})<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			MATCH (rms)-[:RELATED_SHIP_TYPE]->(st:ShipType {typeName: $shipType})
			RETURN {
				totalCargo_ton: rms.totalCargo_ton,
				totalShipCount: rms.totalShipCount,
				totalVoyageCount: rms.totalVoyageCount,
				avgLoadRatio: rms.avgLoadRatio
			} as metrics
		`
		params = map[string]interface{}{
			"entityName": entity.RouteName,
			"shipType":   shipType,
			"period":     period,
		}
	default:
		return nil, fmt.Errorf("unsupported entity type for ship type dimension query: %s", entity.Type)
	}

	// 打印Cypher查询日志
	log.Printf("[queryShipTypeDimension] Executing Cypher query:\n%s\nParams: %+v", query, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[queryShipTypeDimension] Cypher query failed: %v", err)
			return nil, err
		}

		if !records.Next(ctx) {
			log.Printf("[queryShipTypeDimension] No data found for entity: %s, ship type: %s, period: %s", entity.Name, shipType, period)
			return map[string]interface{}{
				"query_mode": "filtered_dimension",
				"filter": map[string]interface{}{
					"dimension": "ship_type",
					"value":     shipType,
				},
				"metrics": map[string]interface{}{},
				"message": "No data found for the specified filter",
			}, nil
		}

		record := records.Record()
		metrics := record.Values[0]

		return map[string]interface{}{
			"query_mode": "filtered_dimension",
			"filter": map[string]interface{}{
				"dimension": "ship_type",
				"value":     shipType,
			},
			"metrics": metrics,
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// queryRouteDimension 查询航线维度的所有指标
func (s *QueryService) queryRouteDimension(ctx context.Context, entity *models.EntityInfo, route string, timeQuery *timeparser.TimeQuery) (interface{}, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	var params map[string]interface{}

	// 暂时使用第一个时间节点，后续可以扩展为多时间节点
	period := timeQuery.MonthNodes[0]

	switch entity.Type {
	case models.EntityTypeShip:
		// 船舶在特定航线的统计
		query = `
			MATCH (s:Ship {name: $entityName})<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)-[:LINE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			WHERE smls.portO + '-' + smls.portD = $route OR smls.portD + '-' + smls.portO = $route
			RETURN {
				cargo_ton: smls.cargo_ton,
				voyage_count: COUNT(smls),
				avg_load_ratio: AVG(smls.load_ratio),
				total_distance: SUM(smls.distance_km)
			} as metrics
		`
		params = map[string]interface{}{
			"entityName": entity.Name,
			"route":      route,
			"period":     period,
		}
	case models.EntityTypeCargoType:
		// 货物类型在特定航线的统计
		query = `
			MATCH (ct:CargoType {subName: $entityName})<-[:ROUTE_CARGO_STAT_FOR_TYPE]-(rmcs:RouteMonthCargoStat)-[:ROUTE_CARGO_STAT_FOR_ROUTE]->(sr:ShippingRoute {routeName: $route})
			MATCH (rmcs)-[:ROUTE_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			RETURN {
				cargo_ton: rmcs.cargo_ton,
				ship_count: rmcs.ship_count,
				voyage_count: rmcs.voyage_count,
				avg_load_ratio: rmcs.avg_load_ratio
			} as metrics
		`
		params = map[string]interface{}{
			"entityName": entity.Name,
			"route":      route,
			"period":     period,
		}
	default:
		return nil, fmt.Errorf("unsupported entity type for route dimension query: %s", entity.Type)
	}

	// 打印Cypher查询日志
	log.Printf("[queryRouteDimension] Executing Cypher query:\n%s\nParams: %+v", query, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[queryRouteDimension] Cypher query failed: %v", err)
			return nil, err
		}

		if !records.Next(ctx) {
			log.Printf("[queryRouteDimension] No data found for entity: %s, route: %s, period: %s", entity.Name, route, period)
			return map[string]interface{}{
				"query_mode": "filtered_dimension",
				"filter": map[string]interface{}{
					"dimension": "route",
					"value":     route,
				},
				"metrics": map[string]interface{}{},
				"message": "No data found for the specified filter",
			}, nil
		}

		record := records.Record()
		metrics := record.Values[0]

		return map[string]interface{}{
			"query_mode": "filtered_dimension",
			"filter": map[string]interface{}{
				"dimension": "route",
				"value":     route,
			},
			"metrics": metrics,
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// executeComprehensiveCompareQuery 执行综合比较查询（复用画像查询逻辑）
func (s *QueryService) executeComprehensiveCompareQuery(ctx context.Context, entityReq *models.EntityRequest, timeQuery *timeparser.TimeQuery) (interface{}, error) {
	// 解析实体列表
	identifiers, isList := entityReq.Identifier.([]interface{})
	if !isList {
		return nil, fmt.Errorf("comprehensive compare query requires entity list")
	}

	var entityNames []string
	for _, id := range identifiers {
		if name, ok := id.(string); ok {
			entityNames = append(entityNames, name)
		}
	}

	if len(entityNames) == 0 {
		return nil, fmt.Errorf("no valid entity names found")
	}

	log.Printf("[executeComprehensiveCompareQuery] Processing %d entities: %v", len(entityNames), entityNames)

	// 为每个实体执行画像查询（现在画像查询已经正确处理时间表达式）
	var compareResults []map[string]interface{}
	for _, entityName := range entityNames {
		// 构建单个实体信息
		entityInfo := &models.EntityInfo{
			Type: entityReq.Type,
			Name: entityName,
		}

		// 复用画像查询逻辑（已修复时间处理）
		profileData, err := s.executeProfileQuery(ctx, entityInfo, timeQuery, nil)
		if err != nil {
			log.Printf("[executeComprehensiveCompareQuery] Profile query failed for entity %s: %v", entityName, err)
			// 继续处理其他实体，不因单个实体失败而中断
			compareResults = append(compareResults, map[string]interface{}{
				"entity_name": entityName,
				"error":       err.Error(),
			})
			continue
		}

		// 包装为比较格式
		entityResult := map[string]interface{}{
			"entity_name": entityName,
		}

		// 如果画像数据是map，直接合并；如果是其他格式，放在data字段
		if profileMap, ok := profileData.(map[string]interface{}); ok {
			for key, value := range profileMap {
				entityResult[key] = value
			}
		} else {
			entityResult["data"] = profileData
		}

		compareResults = append(compareResults, entityResult)
	}

	log.Printf("[executeComprehensiveCompareQuery] Completed comparison for %d entities", len(compareResults))
	return compareResults, nil
}

// // executePortComprehensiveCompare 执行港口综合比较查询（备用方案）
// func (s *QueryService) executePortComprehensiveCompare(ctx context.Context, portNames []string, period string) (interface{}, error) {
// 	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
// 	defer session.Close(ctx)

// 	// 查询港口基础统计信息
// 	basicQuery := `
// 		MATCH (p:Port)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
// 		WHERE p.name IN $portNames
// 		RETURN p.name as port_name, {
// 			inShipCount: pms.inShipCount,
// 			outShipCount: pms.outShipCount,
// 			inCargo_ton: pms.inCargo_ton,
// 			outCargo_ton: pms.outCargo_ton,
// 			totalShipCount: pms.inShipCount + pms.outShipCount,
// 			totalCargo_ton: pms.inCargo_ton + pms.outCargo_ton
// 		} as basic_stats
// 		ORDER BY p.name
// 	`

// 	// 查询港口货物类型分布
// 	cargoQuery := `
// 		MATCH (p:Port)<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
// 		MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
// 		WHERE p.name IN $portNames
// 		RETURN p.name as port_name, collect({
// 			cargo_type: ct.subName,
// 			inCargo_ton: pmcs.inCargo_ton,
// 			outCargo_ton: pmcs.outCargo_ton,
// 			totalCargo_ton: pmcs.inCargo_ton + pmcs.outCargo_ton
// 		}) as cargo_breakdown
// 		ORDER BY p.name
// 	`

// 	// 查询港口航线信息
// 	routeQuery := `
// 		MATCH (p:Port)<-[:ROUTE_STAT_FOR_PORT]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
// 		MATCH (rms)-[:ROUTE_STAT_FOR_ROUTE]->(sr:ShippingRoute)
// 		WHERE p.name IN $portNames
// 		RETURN p.name as port_name, collect({
// 			route_name: sr.routeName,
// 			cargo_ton: rms.totalCargo_ton,
// 			ship_count: rms.totalShipCount,
// 			voyage_count: rms.totalVoyageCount
// 		}) as route_breakdown
// 		ORDER BY p.name
// 	`

// 	log.Printf("[executePortComprehensiveCompare] Executing comprehensive port comparison for: %v, period: %s", portNames, period)

// 	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
// 		// 执行基础统计查询
// 		basicRecords, err := tx.Run(ctx, basicQuery, map[string]interface{}{
// 			"portNames": portNames,
// 			"period":    period,
// 		})
// 		if err != nil {
// 			log.Printf("[executePortComprehensiveCompare] Basic stats query failed: %v", err)
// 			return nil, err
// 		}

// 		// 收集基础统计数据
// 		basicStatsMap := make(map[string]interface{})
// 		for basicRecords.Next(ctx) {
// 			record := basicRecords.Record()
// 			portName := record.Values[0].(string)
// 			stats := record.Values[1]
// 			basicStatsMap[portName] = stats
// 		}

// 		// 执行货物类型查询
// 		cargoRecords, err := tx.Run(ctx, cargoQuery, map[string]interface{}{
// 			"portNames": portNames,
// 			"period":    period,
// 		})
// 		if err != nil {
// 			log.Printf("[executePortComprehensiveCompare] Cargo breakdown query failed: %v", err)
// 			// 继续执行，不因为货物数据缺失而失败
// 		}

// 		// 收集货物类型数据
// 		cargoBreakdownMap := make(map[string]interface{})
// 		if err == nil {
// 			for cargoRecords.Next(ctx) {
// 				record := cargoRecords.Record()
// 				portName := record.Values[0].(string)
// 				breakdown := record.Values[1]
// 				cargoBreakdownMap[portName] = breakdown
// 			}
// 		}

// 		// 执行航线查询
// 		routeRecords, err := tx.Run(ctx, routeQuery, map[string]interface{}{
// 			"portNames": portNames,
// 			"period":    period,
// 		})
// 		if err != nil {
// 			log.Printf("[executePortComprehensiveCompare] Route breakdown query failed: %v", err)
// 			// 继续执行，不因为航线数据缺失而失败
// 		}

// 		// 收集航线数据
// 		routeBreakdownMap := make(map[string]interface{})
// 		if err == nil {
// 			for routeRecords.Next(ctx) {
// 				record := routeRecords.Record()
// 				portName := record.Values[0].(string)
// 				breakdown := record.Values[1]
// 				routeBreakdownMap[portName] = breakdown
// 			}
// 		}

// 		// 组合所有数据
// 		var compareResults []map[string]interface{}
// 		for _, portName := range portNames {
// 			portData := map[string]interface{}{
// 				"entity_name":     portName,
// 				"basic_stats":     basicStatsMap[portName],
// 				"cargo_breakdown": cargoBreakdownMap[portName],
// 				"route_breakdown": routeBreakdownMap[portName],
// 			}
// 			compareResults = append(compareResults, portData)
// 		}

// 		log.Printf("[executePortComprehensiveCompare] Found comprehensive data for %d ports", len(compareResults))
// 		return compareResults, nil
// 	})

// 	if err != nil {
// 		return nil, err
// 	}

// 	return result, nil
// }

// // executeShipComprehensiveCompare 执行船舶综合比较查询（备用方案）
// func (s *QueryService) executeShipComprehensiveCompare(ctx context.Context, shipNames []string, period string) (interface{}, error) {
// 	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
// 	defer session.Close(ctx)

// 	// 查询船舶基础统计信息
// 	basicQuery := `
// 		MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
// 		WHERE s.name IN $shipNames
// 		RETURN s.name as ship_name, {
// 			cargo_ton: sms.cargo_ton,
// 			voyage_count: sms.voyage_count,
// 			distance_km: sms.distance_km,
// 			load_ratio: sms.load_ratio,
// 			dwt: s.dwt,
// 			ship_type: s.shipType
// 		} as basic_stats
// 		ORDER BY s.name
// 	`

// 	// 查询船舶货物类型分布
// 	cargoQuery := `
// 		MATCH (s:Ship)<-[:CARGO_STAT_FOR_SHIP]-(smcs:ShipMonthCargoStat)-[:CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
// 		MATCH (smcs)-[:CARGO_STAT_FOR_TYPE]->(ct:CargoType)
// 		WHERE s.name IN $shipNames
// 		RETURN s.name as ship_name, collect({
// 			cargo_type: ct.subName,
// 			cargo_ton: smcs.cargo_ton,
// 			voyage_count: smcs.voyage_count,
// 			avg_load_ratio: smcs.avg_load_ratio
// 		}) as cargo_breakdown
// 		ORDER BY s.name
// 	`

// 	// 查询船舶航线信息
// 	routeQuery := `
// 		MATCH (s:Ship)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)-[:LINE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
// 		WHERE s.name IN $shipNames
// 		RETURN s.name as ship_name, collect({
// 			route: smls.portO + '-' + smls.portD,
// 			cargo_ton: smls.cargo_ton,
// 			voyage_count: 1,
// 			distance_km: smls.distance_km,
// 			load_ratio: smls.load_ratio
// 		}) as route_breakdown
// 		ORDER BY s.name
// 	`

// 	log.Printf("[executeShipComprehensiveCompare] Executing comprehensive ship comparison for: %v, period: %s", shipNames, period)

// 	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
// 		// 执行基础统计查询
// 		basicRecords, err := tx.Run(ctx, basicQuery, map[string]interface{}{
// 			"shipNames": shipNames,
// 			"period":    period,
// 		})
// 		if err != nil {
// 			log.Printf("[executeShipComprehensiveCompare] Basic stats query failed: %v", err)
// 			return nil, err
// 		}

// 		// 收集基础统计数据
// 		basicStatsMap := make(map[string]interface{})
// 		for basicRecords.Next(ctx) {
// 			record := basicRecords.Record()
// 			shipName := record.Values[0].(string)
// 			stats := record.Values[1]
// 			basicStatsMap[shipName] = stats
// 		}

// 		// 执行货物类型查询
// 		cargoRecords, err := tx.Run(ctx, cargoQuery, map[string]interface{}{
// 			"shipNames": shipNames,
// 			"period":    period,
// 		})
// 		if err != nil {
// 			log.Printf("[executeShipComprehensiveCompare] Cargo breakdown query failed: %v", err)
// 		}

// 		// 收集货物类型数据
// 		cargoBreakdownMap := make(map[string]interface{})
// 		if err == nil {
// 			for cargoRecords.Next(ctx) {
// 				record := cargoRecords.Record()
// 				shipName := record.Values[0].(string)
// 				breakdown := record.Values[1]
// 				cargoBreakdownMap[shipName] = breakdown
// 			}
// 		}

// 		// 执行航线查询
// 		routeRecords, err := tx.Run(ctx, routeQuery, map[string]interface{}{
// 			"shipNames": shipNames,
// 			"period":    period,
// 		})
// 		if err != nil {
// 			log.Printf("[executeShipComprehensiveCompare] Route breakdown query failed: %v", err)
// 		}

// 		// 收集航线数据
// 		routeBreakdownMap := make(map[string]interface{})
// 		if err == nil {
// 			for routeRecords.Next(ctx) {
// 				record := routeRecords.Record()
// 				shipName := record.Values[0].(string)
// 				breakdown := record.Values[1]
// 				routeBreakdownMap[shipName] = breakdown
// 			}
// 		}

// 		// 组合所有数据
// 		var compareResults []map[string]interface{}
// 		for _, shipName := range shipNames {
// 			shipData := map[string]interface{}{
// 				"entity_name":     shipName,
// 				"basic_stats":     basicStatsMap[shipName],
// 				"cargo_breakdown": cargoBreakdownMap[shipName],
// 				"route_breakdown": routeBreakdownMap[shipName],
// 			}
// 			compareResults = append(compareResults, shipData)
// 		}

// 		log.Printf("[executeShipComprehensiveCompare] Found comprehensive data for %d ships", len(compareResults))
// 		return compareResults, nil
// 	})

// 	if err != nil {
// 		return nil, err
// 	}

// 	return result, nil
// }

// removeEntitySuffixes 去除实体名称的常见后缀
func (s *QueryService) removeEntitySuffixes(name string, suffixes []string) string {
	cleanedName := name

	// 尝试去除每个后缀
	for _, suffix := range suffixes {
		if strings.HasSuffix(cleanedName, suffix) {
			cleanedName = strings.TrimSuffix(cleanedName, suffix)
			break // 只去除第一个匹配的后缀
		}
	}

	// 去除前后空格
	cleanedName = strings.TrimSpace(cleanedName)

	// 如果去除后缀后名称为空，返回原名称
	if cleanedName == "" {
		return name
	}

	return cleanedName
}
